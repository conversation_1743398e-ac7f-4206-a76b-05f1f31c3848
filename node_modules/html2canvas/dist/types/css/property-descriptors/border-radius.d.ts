import { IPropertyListDescriptor } from '../IPropertyDescriptor';
import { LengthPercentageTuple } from '../types/length-percentage';
export declare type BorderRadius = LengthPercentageTuple;
export declare const borderTopLeftRadius: IPropertyListDescriptor<BorderRadius>;
export declare const borderTopRightRadius: IPropertyListDescriptor<BorderRadius>;
export declare const borderBottomRightRadius: IPropertyListDescriptor<BorderRadius>;
export declare const borderBottomLeftRadius: IPropertyListDescriptor<BorderRadius>;
