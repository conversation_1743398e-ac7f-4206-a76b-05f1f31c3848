# School Admin Dashboard

A comprehensive school management system built with Next.js, TypeScript, and modern UI components. This dashboard provides administrators with tools to manage students, teachers, classes, attendance, fees, and more.

## Features

### 🔐 Authentication
- Secure admin login system
- Demo mode for testing (<EMAIL> / demo123)
- Session management

### 👥 Student Management
- Add, edit, and delete student profiles
- Student information including personal details, class assignments
- Search and filter functionality
- Student promotion system

### 👨‍🏫 Teacher Management
- Teacher profile management
- Qualification and experience tracking
- Class assignments
- Salary management

### 🏫 Class Management
- Create and manage classes and sections
- Assign teachers to classes
- Set maximum student capacity
- Academic year management

### 📅 Attendance System
- Daily attendance marking
- Multiple status options (Present, Absent, Late, Excused)
- Attendance reports and analytics
- Export functionality

### 📊 Dashboard Analytics
- Real-time statistics and charts
- Student distribution by grade
- Attendance trends
- Performance metrics

### 🗓️ Timetable Management
- Weekly schedule creation
- Subject and teacher assignments
- Room allocation
- Visual timetable grid

### 💰 Fee Management
- Fee structure management
- Payment tracking
- Receipt generation
- Pending payment alerts

### 🔔 Notification System
- Send notifications to different user groups
- Notification history
- Read status tracking

## Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Shadcn/ui components
- **Charts**: Recharts
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Icons**: Lucide React
- **Forms**: React Hook Form with Zod validation

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account (optional for demo mode)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd school-admin-dashboard
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your configuration:
```env
# For demo mode (default)
NEXT_PUBLIC_DEMO_MODE=true
NEXT_PUBLIC_SUPABASE_URL=https://demo.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=demo_key_placeholder

# For production with real Supabase
NEXT_PUBLIC_DEMO_MODE=false
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Demo Login

Use these credentials to access the demo:
- **Email**: <EMAIL>
- **Password**: demo123

## Database Setup (Production)

For production use with Supabase:

1. Create a new Supabase project
2. Run the SQL migrations in `database/migrations/`
3. Update environment variables with your Supabase credentials
4. Set `NEXT_PUBLIC_DEMO_MODE=false`

### Database Schema

The application uses the following main tables:
- `users` - User authentication and roles
- `students` - Student profiles and information
- `teachers` - Teacher profiles and details
- `classes` - Class and section management
- `subjects` - Subject definitions
- `attendance` - Daily attendance records
- `timetable` - Class schedules
- `fees` - Fee management and payments
- `notifications` - System notifications

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── dashboard/         # Dashboard pages
│   ├── login/            # Authentication
│   └── layout.tsx        # Root layout
├── components/           # Reusable components
│   ├── ui/              # Shadcn/ui components
│   └── layout/          # Layout components
├── lib/                 # Utilities and configurations
│   ├── types.ts         # TypeScript definitions
│   ├── utils.ts         # Utility functions
│   └── supabase.ts      # Database client
└── hooks/               # Custom React hooks
```

## Features Overview

### Dashboard
- Overview statistics
- Charts and analytics
- Recent activity feed
- Quick access to all modules

### Student Management
- Complete student profiles
- Class assignments
- Parent contact information
- Academic history

### Teacher Management
- Teacher profiles and qualifications
- Subject specializations
- Class assignments
- Performance tracking

### Attendance System
- Quick attendance marking
- Bulk operations
- Attendance reports
- Export to Excel/PDF

### Fee Management
- Fee structure setup
- Payment tracking
- Receipt generation
- Overdue notifications

### Timetable
- Visual schedule builder
- Conflict detection
- Room management
- Teacher availability

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the demo mode for examples

## Roadmap

- [ ] Mobile responsive improvements
- [ ] Advanced reporting features
- [ ] Parent portal
- [ ] Student portal
- [ ] SMS notifications
- [ ] Exam management
- [ ] Library management
- [ ] Transport management
