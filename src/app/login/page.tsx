'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { auth } from '@/lib/supabase';
import { toast } from 'sonner';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Demo mode - skip actual authentication
      if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
        if (email === '<EMAIL>' && password === 'demo123') {
          // Store user role in localStorage for demo
          localStorage.setItem('userRole', 'admin');
          localStorage.setItem('userId', 'admin-1');
          toast.success('Admin login successful!');
          router.push('/dashboard');
        } else if (email === '<EMAIL>' && password === 'demo123') {
          // Store user role in localStorage for demo
          localStorage.setItem('userRole', 'teacher');
          localStorage.setItem('userId', 'teacher-1');
          localStorage.setItem('teacherName', 'Sarah Johnson');
          toast.success('Teacher login successful!');
          router.push('/teacher');
        } else {
          toast.error('Invalid credentials. Check demo credentials below.');
        }
        return;
      }

      // Real Supabase authentication
      const { data, error } = await auth.signIn(email, password);

      if (error) {
        toast.error(error.message);
        return;
      }

      if (data.user) {
        // In real implementation, get user role from database
        toast.success('Login successful!');
        router.push('/dashboard');
      }
    } catch (error) {
      toast.error('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl text-center">School Portal Login</CardTitle>
          <CardDescription className="text-center">
            Enter your credentials to access your dashboard
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? 'Signing in...' : 'Sign In'}
            </Button>
          </form>

          <div className="mt-6 space-y-3">
            <div className="p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800 font-medium">Admin Demo:</p>
              <p className="text-sm text-blue-600">Email: <EMAIL></p>
              <p className="text-sm text-blue-600">Password: demo123</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <p className="text-sm text-green-800 font-medium">Teacher Demo:</p>
              <p className="text-sm text-green-600">Email: <EMAIL></p>
              <p className="text-sm text-green-600">Password: demo123</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
