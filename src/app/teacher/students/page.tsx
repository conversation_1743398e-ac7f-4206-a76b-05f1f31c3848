'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Search, Eye, MessageSquare, Calendar, Phone, Mail } from 'lucide-react';

// Mock data for teacher's students
const mockStudents = [
  {
    id: '1',
    student_id: 'STU001',
    first_name: '<PERSON>',
    last_name: 'Doe',
    class: 'Grade 5-A',
    attendance_rate: 95.2,
    last_attendance: '2024-01-19',
    status: 'present',
    parent_email: '<EMAIL>',
    phone: '555-0123',
    performance: 'excellent',
  },
  {
    id: '2',
    student_id: 'STU002',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    class: 'Grade 5-A',
    attendance_rate: 88.7,
    last_attendance: '2024-01-19',
    status: 'present',
    parent_email: '<EMAIL>',
    phone: '555-0124',
    performance: 'good',
  },
  {
    id: '3',
    student_id: 'STU003',
    first_name: 'Mike',
    last_name: 'Johnson',
    class: 'Grade 5-B',
    attendance_rate: 92.1,
    last_attendance: '2024-01-18',
    status: 'absent',
    parent_email: '<EMAIL>',
    phone: '555-0125',
    performance: 'good',
  },
  {
    id: '4',
    student_id: 'STU004',
    first_name: 'Sarah',
    last_name: 'Wilson',
    class: 'Grade 6-A',
    attendance_rate: 97.8,
    last_attendance: '2024-01-19',
    status: 'present',
    parent_email: '<EMAIL>',
    phone: '555-0126',
    performance: 'excellent',
  },
  {
    id: '5',
    student_id: 'STU005',
    first_name: 'David',
    last_name: 'Brown',
    class: 'Grade 6-A',
    attendance_rate: 85.3,
    last_attendance: '2024-01-19',
    status: 'late',
    parent_email: '<EMAIL>',
    phone: '555-0127',
    performance: 'average',
  },
];

const mockClasses = [
  { id: 'all', name: 'All Classes' },
  { id: 'grade5a', name: 'Grade 5-A' },
  { id: 'grade5b', name: 'Grade 5-B' },
  { id: 'grade6a', name: 'Grade 6-A' },
];

export default function TeacherStudentsPage() {
  const [students] = useState(mockStudents);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('all');
  const [selectedStudent, setSelectedStudent] = useState<any>(null);

  const filteredStudents = students.filter(student => {
    const matchesSearch = `${student.first_name} ${student.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.student_id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = selectedClass === 'all' || student.class.toLowerCase().includes(selectedClass.replace('grade', 'grade '));
    return matchesSearch && matchesClass;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      present: 'default',
      absent: 'destructive',
      late: 'secondary',
      excused: 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status}
      </Badge>
    );
  };

  const getPerformanceBadge = (performance: string) => {
    const variants = {
      excellent: 'default',
      good: 'secondary',
      average: 'outline',
      poor: 'destructive'
    } as const;

    const colors = {
      excellent: 'bg-green-50 text-green-700 border-green-200',
      good: 'bg-blue-50 text-blue-700 border-blue-200',
      average: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      poor: 'bg-red-50 text-red-700 border-red-200'
    };

    return (
      <Badge variant="outline" className={colors[performance as keyof typeof colors]}>
        {performance}
      </Badge>
    );
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Students</h1>
        <p className="text-gray-600">Manage and view information about students in your classes</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-blue-600">{filteredStudents.length}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-50">
                <Search className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Present Today</p>
                <p className="text-2xl font-bold text-green-600">
                  {filteredStudents.filter(s => s.status === 'present').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-50">
                <Calendar className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Absent Today</p>
                <p className="text-2xl font-bold text-red-600">
                  {filteredStudents.filter(s => s.status === 'absent').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-red-50">
                <Calendar className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Attendance</p>
                <p className="text-2xl font-bold text-purple-600">
                  {(filteredStudents.reduce((sum, s) => sum + s.attendance_rate, 0) / filteredStudents.length).toFixed(1)}%
                </p>
              </div>
              <div className="p-3 rounded-full bg-purple-50">
                <Calendar className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search students by name or ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {mockClasses.map(cls => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Students Table */}
      <Card>
        <CardHeader>
          <CardTitle>Students List</CardTitle>
          <CardDescription>
            {filteredStudents.length} students found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Class</TableHead>
                <TableHead>Attendance Rate</TableHead>
                <TableHead>Last Attendance</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStudents.map((student) => (
                <TableRow key={student.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          {getInitials(student.first_name, student.last_name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-gray-900">
                          {student.first_name} {student.last_name}
                        </p>
                        <p className="text-sm text-gray-500">{student.student_id}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{student.class}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{student.attendance_rate}%</span>
                      {getStatusBadge(student.status)}
                    </div>
                  </TableCell>
                  <TableCell>{student.last_attendance}</TableCell>
                  <TableCell>{getPerformanceBadge(student.performance)}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MessageSquare className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Phone className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
