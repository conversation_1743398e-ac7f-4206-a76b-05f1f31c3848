'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { TeacherSidebar } from '@/components/layout/teacher-sidebar';

export default function TeacherLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [loading, setLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Demo mode - check localStorage for teacher role
        if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
          const userRole = localStorage.getItem('userRole');
          
          if (userRole !== 'teacher') {
            router.push('/login');
            return;
          }
          
          setAuthenticated(true);
          setLoading(false);
          return;
        }

        // Real authentication check would go here
        setAuthenticated(true);
      } catch (error) {
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (!authenticated) {
    return null;
  }

  return (
    <div className="h-screen flex bg-gray-50">
      <TeacherSidebar />
      <main className="flex-1 overflow-auto">
        <div className="p-6">
          {children}
        </div>
      </main>
    </div>
  );
}
