'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { User, Bell, Lock, BookOpen } from 'lucide-react';
import { toast } from 'sonner';

export default function TeacherSettingsPage() {
  const [teacherInfo, setTeacherInfo] = useState({
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone: '555-0123',
    address: '123 Teacher Lane, Education City',
    qualification: 'M.Ed in Mathematics',
    experience_years: 8,
    bio: 'Passionate mathematics teacher with 8 years of experience in elementary education.',
  });

  const [preferences, setPreferences] = useState({
    email_notifications: true,
    sms_notifications: false,
    attendance_reminders: true,
    performance_alerts: true,
    class_updates: true,
    theme: 'light',
    language: 'english',
  });

  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });

  useEffect(() => {
    // Get teacher name from localStorage for demo
    const name = localStorage.getItem('teacherName') || 'Sarah Johnson';
    const [firstName, lastName] = name.split(' ');
    setTeacherInfo(prev => ({
      ...prev,
      first_name: firstName || 'Sarah',
      last_name: lastName || 'Johnson',
    }));
  }, []);

  const handleSaveProfile = () => {
    // In a real app, this would save to the database
    localStorage.setItem('teacherName', `${teacherInfo.first_name} ${teacherInfo.last_name}`);
    toast.success('Profile updated successfully');
  };

  const handleSavePreferences = () => {
    // In a real app, this would save to the database
    toast.success('Preferences updated successfully');
  };

  const handleChangePassword = () => {
    if (passwordData.new_password !== passwordData.confirm_password) {
      toast.error('New passwords do not match');
      return;
    }
    
    // In a real app, this would update the password
    toast.success('Password changed successfully');
    setPasswordData({
      current_password: '',
      new_password: '',
      confirm_password: '',
    });
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Manage your profile and preferences</p>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile" className="flex items-center space-x-2">
            <User className="h-4 w-4" />
            <span>Profile</span>
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex items-center space-x-2">
            <Bell className="h-4 w-4" />
            <span>Preferences</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center space-x-2">
            <Lock className="h-4 w-4" />
            <span>Security</span>
          </TabsTrigger>
          <TabsTrigger value="classes" className="flex items-center space-x-2">
            <BookOpen className="h-4 w-4" />
            <span>My Classes</span>
          </TabsTrigger>
        </TabsList>

        {/* Profile Settings */}
        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal information and professional details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Profile Picture */}
              <div className="flex items-center space-x-4">
                <Avatar className="w-20 h-20">
                  <AvatarFallback className="bg-green-100 text-green-600 text-xl">
                    {getInitials(teacherInfo.first_name, teacherInfo.last_name)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <Button variant="outline">Change Photo</Button>
                  <p className="text-sm text-gray-500 mt-1">JPG, PNG or GIF. Max size 2MB.</p>
                </div>
              </div>

              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first_name">First Name</Label>
                  <Input
                    id="first_name"
                    value={teacherInfo.first_name}
                    onChange={(e) => setTeacherInfo(prev => ({ ...prev, first_name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="last_name">Last Name</Label>
                  <Input
                    id="last_name"
                    value={teacherInfo.last_name}
                    onChange={(e) => setTeacherInfo(prev => ({ ...prev, last_name: e.target.value }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={teacherInfo.email}
                    onChange={(e) => setTeacherInfo(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={teacherInfo.phone}
                    onChange={(e) => setTeacherInfo(prev => ({ ...prev, phone: e.target.value }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={teacherInfo.address}
                  onChange={(e) => setTeacherInfo(prev => ({ ...prev, address: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="qualification">Qualification</Label>
                  <Input
                    id="qualification"
                    value={teacherInfo.qualification}
                    onChange={(e) => setTeacherInfo(prev => ({ ...prev, qualification: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="experience">Experience (Years)</Label>
                  <Input
                    id="experience"
                    type="number"
                    value={teacherInfo.experience_years}
                    onChange={(e) => setTeacherInfo(prev => ({ ...prev, experience_years: parseInt(e.target.value) || 0 }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  value={teacherInfo.bio}
                  onChange={(e) => setTeacherInfo(prev => ({ ...prev, bio: e.target.value }))}
                  placeholder="Tell us about yourself..."
                  rows={4}
                />
              </div>

              <Button onClick={handleSaveProfile}>
                Save Profile
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Preferences */}
        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Configure how you want to receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email_notifications">Email Notifications</Label>
                    <p className="text-sm text-gray-600">Receive notifications via email</p>
                  </div>
                  <input
                    type="checkbox"
                    id="email_notifications"
                    checked={preferences.email_notifications}
                    onChange={(e) => setPreferences(prev => ({ ...prev, email_notifications: e.target.checked }))}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sms_notifications">SMS Notifications</Label>
                    <p className="text-sm text-gray-600">Receive notifications via SMS</p>
                  </div>
                  <input
                    type="checkbox"
                    id="sms_notifications"
                    checked={preferences.sms_notifications}
                    onChange={(e) => setPreferences(prev => ({ ...prev, sms_notifications: e.target.checked }))}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="attendance_reminders">Attendance Reminders</Label>
                    <p className="text-sm text-gray-600">Get reminders to mark attendance</p>
                  </div>
                  <input
                    type="checkbox"
                    id="attendance_reminders"
                    checked={preferences.attendance_reminders}
                    onChange={(e) => setPreferences(prev => ({ ...prev, attendance_reminders: e.target.checked }))}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="performance_alerts">Performance Alerts</Label>
                    <p className="text-sm text-gray-600">Get notified about student performance issues</p>
                  </div>
                  <input
                    type="checkbox"
                    id="performance_alerts"
                    checked={preferences.performance_alerts}
                    onChange={(e) => setPreferences(prev => ({ ...prev, performance_alerts: e.target.checked }))}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="class_updates">Class Updates</Label>
                    <p className="text-sm text-gray-600">Receive updates about your classes</p>
                  </div>
                  <input
                    type="checkbox"
                    id="class_updates"
                    checked={preferences.class_updates}
                    onChange={(e) => setPreferences(prev => ({ ...prev, class_updates: e.target.checked }))}
                    className="rounded"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="theme">Theme</Label>
                  <Select value={preferences.theme} onValueChange={(value) => setPreferences(prev => ({ ...prev, theme: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="system">System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="language">Language</Label>
                  <Select value={preferences.language} onValueChange={(value) => setPreferences(prev => ({ ...prev, language: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="english">English</SelectItem>
                      <SelectItem value="spanish">Spanish</SelectItem>
                      <SelectItem value="french">French</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button onClick={handleSavePreferences}>
                Save Preferences
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Manage your password and security preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="current_password">Current Password</Label>
                <Input
                  id="current_password"
                  type="password"
                  value={passwordData.current_password}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, current_password: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="new_password">New Password</Label>
                <Input
                  id="new_password"
                  type="password"
                  value={passwordData.new_password}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, new_password: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="confirm_password">Confirm New Password</Label>
                <Input
                  id="confirm_password"
                  type="password"
                  value={passwordData.confirm_password}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, confirm_password: e.target.value }))}
                />
              </div>

              <Button onClick={handleChangePassword}>
                Change Password
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* My Classes */}
        <TabsContent value="classes">
          <Card>
            <CardHeader>
              <CardTitle>My Classes</CardTitle>
              <CardDescription>
                Classes assigned to you
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium">Grade 5-A</h3>
                  <p className="text-sm text-gray-600">Mathematics • 25 students • Room 101</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium">Grade 5-B</h3>
                  <p className="text-sm text-gray-600">Mathematics • 23 students • Room 102</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium">Grade 6-A</h3>
                  <p className="text-sm text-gray-600">Mathematics • 27 students • Room 103</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
