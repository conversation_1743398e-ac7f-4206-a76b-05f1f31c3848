'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Save, History, Edit, Users } from 'lucide-react';
import { toast } from 'sonner';

// Mock data for attendance
const mockStudents = [
  { id: '1', student_id: 'STU001', first_name: '<PERSON>', last_name: '<PERSON><PERSON>', class: 'Grade 5-A' },
  { id: '2', student_id: 'STU002', first_name: '<PERSON>', last_name: '<PERSON>', class: 'Grade 5-A' },
  { id: '3', student_id: 'STU003', first_name: 'Mike', last_name: 'Johnson', class: 'Grade 5-A' },
  { id: '4', student_id: 'STU004', first_name: 'Sarah', last_name: 'Wilson', class: 'Grade 5-A' },
  { id: '5', student_id: 'STU005', first_name: 'David', last_name: 'Brown', class: 'Grade 5-A' },
];

const mockClasses = [
  { id: 'grade5a', name: 'Grade 5-A', students: 25 },
  { id: 'grade5b', name: 'Grade 5-B', students: 23 },
  { id: 'grade6a', name: 'Grade 6-A', students: 27 },
];

const mockAttendanceHistory = [
  { date: '2024-01-19', class: 'Grade 5-A', present: 23, absent: 2, late: 0, total: 25 },
  { date: '2024-01-18', class: 'Grade 5-A', present: 24, absent: 1, late: 0, total: 25 },
  { date: '2024-01-17', class: 'Grade 5-A', present: 22, absent: 2, late: 1, total: 25 },
  { date: '2024-01-16', class: 'Grade 5-A', present: 25, absent: 0, late: 0, total: 25 },
  { date: '2024-01-15', class: 'Grade 5-A', present: 23, absent: 1, late: 1, total: 25 },
];

export default function TeacherAttendancePage() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedClass, setSelectedClass] = useState('grade5a');
  const [attendance, setAttendance] = useState<Record<string, 'present' | 'absent' | 'late' | 'excused'>>({});
  const [editMode, setEditMode] = useState(false);

  const handleAttendanceChange = (studentId: string, status: 'present' | 'absent' | 'late' | 'excused') => {
    setAttendance(prev => ({
      ...prev,
      [studentId]: status
    }));
  };

  const handleSaveAttendance = () => {
    // In a real app, this would save to the database
    toast.success('Attendance saved successfully');
    setEditMode(false);
  };

  const handleEditAttendance = () => {
    setEditMode(true);
    toast.info('Edit mode enabled. Make your changes and save.');
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      present: 'default',
      absent: 'destructive',
      late: 'secondary',
      excused: 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status}
      </Badge>
    );
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`;
  };

  const selectedClassData = mockClasses.find(c => c.id === selectedClass);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Attendance Management</h1>
        <p className="text-gray-600">Mark and manage student attendance for your classes</p>
      </div>

      <Tabs defaultValue="mark" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="mark" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>Mark Attendance</span>
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center space-x-2">
            <History className="h-4 w-4" />
            <span>Attendance History</span>
          </TabsTrigger>
        </TabsList>

        {/* Mark Attendance Tab */}
        <TabsContent value="mark">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Mark Attendance</CardTitle>
              <CardDescription>Select date and class to mark attendance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="date">Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="class">Class</Label>
                  <Select value={selectedClass} onValueChange={setSelectedClass}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {mockClasses.map(cls => (
                        <SelectItem key={cls.id} value={cls.id}>
                          {cls.name} ({cls.students} students)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end space-x-2">
                  <Button onClick={handleSaveAttendance} className="flex-1">
                    <Save className="h-4 w-4 mr-2" />
                    Save Attendance
                  </Button>
                  {!editMode && (
                    <Button variant="outline" onClick={handleEditAttendance}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Attendance Summary */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Present</p>
                    <p className="text-2xl font-bold text-green-600">
                      {Object.values(attendance).filter(status => status === 'present').length}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-green-50">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Absent</p>
                    <p className="text-2xl font-bold text-red-600">
                      {Object.values(attendance).filter(status => status === 'absent').length}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-red-50">
                    <Users className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Late</p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {Object.values(attendance).filter(status => status === 'late').length}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-yellow-50">
                    <Users className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {selectedClassData?.students || 0}
                    </p>
                  </div>
                  <div className="p-3 rounded-full bg-blue-50">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Students Attendance */}
          <Card>
            <CardHeader>
              <CardTitle>Students - {selectedClassData?.name}</CardTitle>
              <CardDescription>
                Mark attendance for {selectedDate}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Student ID</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockStudents.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarFallback className="bg-blue-100 text-blue-600">
                              {getInitials(student.first_name, student.last_name)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-gray-900">
                              {student.first_name} {student.last_name}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{student.student_id}</TableCell>
                      <TableCell>
                        {attendance[student.id] ? getStatusBadge(attendance[student.id]) : (
                          <Badge variant="outline">Not Marked</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant={attendance[student.id] === 'present' ? 'default' : 'outline'}
                            onClick={() => handleAttendanceChange(student.id, 'present')}
                            disabled={!editMode && attendance[student.id] !== undefined}
                          >
                            Present
                          </Button>
                          <Button
                            size="sm"
                            variant={attendance[student.id] === 'absent' ? 'destructive' : 'outline'}
                            onClick={() => handleAttendanceChange(student.id, 'absent')}
                            disabled={!editMode && attendance[student.id] !== undefined}
                          >
                            Absent
                          </Button>
                          <Button
                            size="sm"
                            variant={attendance[student.id] === 'late' ? 'secondary' : 'outline'}
                            onClick={() => handleAttendanceChange(student.id, 'late')}
                            disabled={!editMode && attendance[student.id] !== undefined}
                          >
                            Late
                          </Button>
                          <Button
                            size="sm"
                            variant={attendance[student.id] === 'excused' ? 'outline' : 'outline'}
                            onClick={() => handleAttendanceChange(student.id, 'excused')}
                            disabled={!editMode && attendance[student.id] !== undefined}
                          >
                            Excused
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Attendance History Tab */}
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Attendance History</CardTitle>
              <CardDescription>View past attendance records for your classes</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Class</TableHead>
                    <TableHead>Present</TableHead>
                    <TableHead>Absent</TableHead>
                    <TableHead>Late</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead>Attendance Rate</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockAttendanceHistory.map((record, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{record.date}</TableCell>
                      <TableCell>{record.class}</TableCell>
                      <TableCell>
                        <Badge variant="default">{record.present}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="destructive">{record.absent}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{record.late}</Badge>
                      </TableCell>
                      <TableCell>{record.total}</TableCell>
                      <TableCell>
                        <span className="font-medium">
                          {((record.present / record.total) * 100).toFixed(1)}%
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
