'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, Calendar, Clock, MessageSquare, BookOpen, TrendingUp } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';

// Mock data for teacher dashboard
const mockTeacherStats = {
  total_students: 85,
  classes_assigned: 3,
  attendance_rate: 94.2,
  pending_remarks: 5,
  upcoming_classes: 4,
  completed_classes_today: 2,
};

const mockAttendanceData = [
  { date: '2024-01-15', present: 80, absent: 5, total: 85 },
  { date: '2024-01-16', present: 82, absent: 3, total: 85 },
  { date: '2024-01-17', present: 85, absent: 0, total: 85 },
  { date: '2024-01-18', present: 78, absent: 7, total: 85 },
  { date: '2024-01-19', present: 83, absent: 2, total: 85 },
];

const mockUpcomingClasses = [
  { time: '09:00 AM', class: 'Grade 5-A', subject: 'Mathematics', room: 'Room 101' },
  { time: '10:30 AM', class: 'Grade 5-B', subject: 'Mathematics', room: 'Room 102' },
  { time: '02:00 PM', class: 'Grade 6-A', subject: 'Mathematics', room: 'Room 103' },
  { time: '03:30 PM', class: 'Grade 6-B', subject: 'Mathematics', room: 'Room 104' },
];

const mockRecentActivity = [
  { action: 'Marked attendance for Grade 5-A', time: '2 hours ago', type: 'attendance' },
  { action: 'Added performance remark for John Doe', time: '4 hours ago', type: 'remark' },
  { action: 'Updated lesson plan for Mathematics', time: '1 day ago', type: 'lesson' },
  { action: 'Submitted monthly report', time: '2 days ago', type: 'report' },
];

export default function TeacherDashboard() {
  const [teacherName, setTeacherName] = useState('Teacher');

  useEffect(() => {
    // Get teacher name from localStorage for demo
    const name = localStorage.getItem('teacherName') || 'Teacher';
    setTeacherName(name);
  }, []);

  const statCards = [
    {
      title: 'My Students',
      value: mockTeacherStats.total_students.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Classes Assigned',
      value: mockTeacherStats.classes_assigned.toString(),
      icon: BookOpen,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Attendance Rate',
      value: `${mockTeacherStats.attendance_rate}%`,
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Pending Remarks',
      value: mockTeacherStats.pending_remarks.toString(),
      icon: MessageSquare,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Today\'s Classes',
      value: `${mockTeacherStats.completed_classes_today}/${mockTeacherStats.upcoming_classes}`,
      icon: Clock,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
    {
      title: 'Performance',
      value: 'Excellent',
      icon: TrendingUp,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Welcome back, {teacherName}!</h1>
        <p className="text-gray-600">Here's what's happening with your classes today</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                </div>
                <div className={`p-3 rounded-full ${card.bgColor}`}>
                  <card.icon className={`h-6 w-6 ${card.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts and Schedule */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Attendance Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Attendance Overview</CardTitle>
            <CardDescription>Student attendance in your classes</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={mockAttendanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="present" fill="#10b981" name="Present" />
                <Bar dataKey="absent" fill="#ef4444" name="Absent" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Today's Schedule */}
        <Card>
          <CardHeader>
            <CardTitle>Today's Schedule</CardTitle>
            <CardDescription>Your upcoming classes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockUpcomingClasses.map((cls, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium text-gray-900">{cls.class}</p>
                      <p className="text-sm text-gray-600">{cls.subject}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{cls.time}</p>
                    <p className="text-xs text-gray-500">{cls.room}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks you can perform</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              <Button variant="outline" className="h-20 flex-col space-y-2">
                <Calendar className="h-6 w-6" />
                <span className="text-sm">Mark Attendance</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col space-y-2">
                <MessageSquare className="h-6 w-6" />
                <span className="text-sm">Add Remarks</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col space-y-2">
                <Users className="h-6 w-6" />
                <span className="text-sm">View Students</span>
              </Button>
              <Button variant="outline" className="h-20 flex-col space-y-2">
                <Clock className="h-6 w-6" />
                <span className="text-sm">My Timetable</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your latest actions and updates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRecentActivity.map((activity, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.action}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {activity.type}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
