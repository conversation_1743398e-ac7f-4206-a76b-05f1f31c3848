'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Search, MessageSquare, Star, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { toast } from 'sonner';

// Mock data for students and remarks
const mockStudents = [
  { id: '1', student_id: 'STU001', first_name: 'John', last_name: 'Doe', class: 'Grade 5-A' },
  { id: '2', student_id: 'STU002', first_name: 'Jane', last_name: 'Smith', class: 'Grade 5-A' },
  { id: '3', student_id: 'STU003', first_name: 'Mike', last_name: 'Johnson', class: 'Grade 5-B' },
  { id: '4', student_id: 'STU004', first_name: 'Sarah', last_name: 'Wilson', class: 'Grade 6-A' },
  { id: '5', student_id: 'STU005', first_name: 'David', last_name: 'Brown', class: 'Grade 6-A' },
];

const mockRemarks = [
  {
    id: '1',
    student_id: '1',
    student_name: 'John Doe',
    class: 'Grade 5-A',
    subject: 'Mathematics',
    performance_type: 'excellent',
    remark: 'John has shown exceptional understanding of algebraic concepts. His problem-solving skills are outstanding.',
    date: '2024-01-19',
    teacher_name: 'Sarah Johnson',
    status: 'sent',
  },
  {
    id: '2',
    student_id: '2',
    student_name: 'Jane Smith',
    class: 'Grade 5-A',
    subject: 'Mathematics',
    performance_type: 'good',
    remark: 'Jane is making steady progress in mathematics. She actively participates in class discussions.',
    date: '2024-01-18',
    teacher_name: 'Sarah Johnson',
    status: 'sent',
  },
  {
    id: '3',
    student_id: '3',
    student_name: 'Mike Johnson',
    class: 'Grade 5-B',
    subject: 'Mathematics',
    performance_type: 'needs_improvement',
    remark: 'Mike needs to focus more on homework completion. Additional practice in basic arithmetic is recommended.',
    date: '2024-01-17',
    teacher_name: 'Sarah Johnson',
    status: 'draft',
  },
];

const performanceTypes = [
  { value: 'excellent', label: 'Excellent', icon: Star, color: 'text-green-600', bgColor: 'bg-green-50' },
  { value: 'good', label: 'Good', icon: TrendingUp, color: 'text-blue-600', bgColor: 'bg-blue-50' },
  { value: 'average', label: 'Average', icon: Minus, color: 'text-yellow-600', bgColor: 'bg-yellow-50' },
  { value: 'needs_improvement', label: 'Needs Improvement', icon: TrendingDown, color: 'text-red-600', bgColor: 'bg-red-50' },
];

export default function TeacherRemarksPage() {
  const [remarks, setRemarks] = useState(mockRemarks);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    student_id: '',
    subject: 'Mathematics',
    performance_type: 'good',
    remark: '',
  });

  const filteredRemarks = remarks.filter(remark =>
    remark.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    remark.class.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const selectedStudent = mockStudents.find(s => s.id === formData.student_id);
    if (!selectedStudent) return;

    const newRemark = {
      id: Date.now().toString(),
      student_id: formData.student_id,
      student_name: `${selectedStudent.first_name} ${selectedStudent.last_name}`,
      class: selectedStudent.class,
      subject: formData.subject,
      performance_type: formData.performance_type,
      remark: formData.remark,
      date: new Date().toISOString().split('T')[0],
      teacher_name: 'Sarah Johnson',
      status: 'sent',
    };

    setRemarks(prev => [newRemark, ...prev]);
    toast.success('Performance remark added successfully');
    
    setFormData({
      student_id: '',
      subject: 'Mathematics',
      performance_type: 'good',
      remark: '',
    });
    setIsDialogOpen(false);
  };

  const getPerformanceBadge = (type: string) => {
    const performanceType = performanceTypes.find(p => p.value === type);
    if (!performanceType) return null;

    return (
      <Badge variant="outline" className={`${performanceType.bgColor} ${performanceType.color} border-current`}>
        <performanceType.icon className="h-3 w-3 mr-1" />
        {performanceType.label}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      sent: 'default',
      draft: 'secondary',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status}
      </Badge>
    );
  };

  const getInitials = (name: string) => {
    const names = name.split(' ');
    return names.map(n => n.charAt(0)).join('');
  };

  const remarkStats = {
    total: remarks.length,
    sent: remarks.filter(r => r.status === 'sent').length,
    drafts: remarks.filter(r => r.status === 'draft').length,
    thisWeek: remarks.filter(r => {
      const remarkDate = new Date(r.date);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return remarkDate >= weekAgo;
    }).length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Performance Remarks</h1>
          <p className="text-gray-600">Send performance feedback to students and parents</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Remark
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add Performance Remark</DialogTitle>
              <DialogDescription>
                Send performance feedback for a student
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="student">Student</Label>
                  <Select value={formData.student_id} onValueChange={(value) => setFormData(prev => ({ ...prev, student_id: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select student" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockStudents.map(student => (
                        <SelectItem key={student.id} value={student.id}>
                          {student.first_name} {student.last_name} ({student.class})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="subject">Subject</Label>
                  <Select value={formData.subject} onValueChange={(value) => setFormData(prev => ({ ...prev, subject: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Mathematics">Mathematics</SelectItem>
                      <SelectItem value="English">English</SelectItem>
                      <SelectItem value="Science">Science</SelectItem>
                      <SelectItem value="History">History</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="performance_type">Performance Type</Label>
                <Select value={formData.performance_type} onValueChange={(value) => setFormData(prev => ({ ...prev, performance_type: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {performanceTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center space-x-2">
                          <type.icon className={`h-4 w-4 ${type.color}`} />
                          <span>{type.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="remark">Remark</Label>
                <Textarea
                  id="remark"
                  value={formData.remark}
                  onChange={(e) => setFormData(prev => ({ ...prev, remark: e.target.value }))}
                  placeholder="Enter detailed performance feedback..."
                  rows={4}
                  required
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Send Remark
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Remarks</p>
                <p className="text-2xl font-bold text-blue-600">{remarkStats.total}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-50">
                <MessageSquare className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Sent</p>
                <p className="text-2xl font-bold text-green-600">{remarkStats.sent}</p>
              </div>
              <div className="p-3 rounded-full bg-green-50">
                <MessageSquare className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Drafts</p>
                <p className="text-2xl font-bold text-yellow-600">{remarkStats.drafts}</p>
              </div>
              <div className="p-3 rounded-full bg-yellow-50">
                <MessageSquare className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">This Week</p>
                <p className="text-2xl font-bold text-purple-600">{remarkStats.thisWeek}</p>
              </div>
              <div className="p-3 rounded-full bg-purple-50">
                <MessageSquare className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search remarks by student name or class..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Remarks List */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Remarks</CardTitle>
          <CardDescription>
            {filteredRemarks.length} remarks found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Subject</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRemarks.map((remark) => (
                <TableRow key={remark.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarFallback className="bg-blue-100 text-blue-600">
                          {getInitials(remark.student_name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-gray-900">{remark.student_name}</p>
                        <p className="text-sm text-gray-500">{remark.class}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{remark.subject}</Badge>
                  </TableCell>
                  <TableCell>{getPerformanceBadge(remark.performance_type)}</TableCell>
                  <TableCell>{remark.date}</TableCell>
                  <TableCell>{getStatusBadge(remark.status)}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">
                        View
                      </Button>
                      <Button variant="ghost" size="sm">
                        Edit
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
