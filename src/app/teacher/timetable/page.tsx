'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Clock, Calendar, MapPin, Users, BookOpen } from 'lucide-react';

// Mock data for teacher's timetable
const mockTimetable = [
  {
    id: '1',
    day: 'Monday',
    day_number: 1,
    time: '09:00 - 10:00',
    start_time: '09:00',
    end_time: '10:00',
    subject: 'Mathematics',
    class: 'Grade 5-A',
    room: 'Room 101',
    students: 25,
  },
  {
    id: '2',
    day: 'Monday',
    day_number: 1,
    time: '10:30 - 11:30',
    start_time: '10:30',
    end_time: '11:30',
    subject: 'Mathematics',
    class: 'Grade 5-B',
    room: 'Room 102',
    students: 23,
  },
  {
    id: '3',
    day: 'Monday',
    day_number: 1,
    time: '14:00 - 15:00',
    start_time: '14:00',
    end_time: '15:00',
    subject: 'Mathematics',
    class: 'Grade 6-A',
    room: 'Room 103',
    students: 27,
  },
  {
    id: '4',
    day: 'Tuesday',
    day_number: 2,
    time: '09:00 - 10:00',
    start_time: '09:00',
    end_time: '10:00',
    subject: 'Mathematics',
    class: 'Grade 5-A',
    room: 'Room 101',
    students: 25,
  },
  {
    id: '5',
    day: 'Tuesday',
    day_number: 2,
    time: '11:00 - 12:00',
    start_time: '11:00',
    end_time: '12:00',
    subject: 'Mathematics',
    class: 'Grade 6-A',
    room: 'Room 103',
    students: 27,
  },
  {
    id: '6',
    day: 'Wednesday',
    day_number: 3,
    time: '10:00 - 11:00',
    start_time: '10:00',
    end_time: '11:00',
    subject: 'Mathematics',
    class: 'Grade 5-B',
    room: 'Room 102',
    students: 23,
  },
  {
    id: '7',
    day: 'Wednesday',
    day_number: 3,
    time: '13:00 - 14:00',
    start_time: '13:00',
    end_time: '14:00',
    subject: 'Mathematics',
    class: 'Grade 6-A',
    room: 'Room 103',
    students: 27,
  },
  {
    id: '8',
    day: 'Thursday',
    day_number: 4,
    time: '09:30 - 10:30',
    start_time: '09:30',
    end_time: '10:30',
    subject: 'Mathematics',
    class: 'Grade 5-A',
    room: 'Room 101',
    students: 25,
  },
  {
    id: '9',
    day: 'Thursday',
    day_number: 4,
    time: '15:00 - 16:00',
    start_time: '15:00',
    end_time: '16:00',
    subject: 'Mathematics',
    class: 'Grade 5-B',
    room: 'Room 102',
    students: 23,
  },
  {
    id: '10',
    day: 'Friday',
    day_number: 5,
    time: '08:30 - 09:30',
    start_time: '08:30',
    end_time: '09:30',
    subject: 'Mathematics',
    class: 'Grade 6-A',
    room: 'Room 103',
    students: 27,
  },
  {
    id: '11',
    day: 'Friday',
    day_number: 5,
    time: '14:30 - 15:30',
    start_time: '14:30',
    end_time: '15:30',
    subject: 'Mathematics',
    class: 'Grade 5-A',
    room: 'Room 101',
    students: 25,
  },
];

const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
const timeSlots = [
  '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', 
  '12:00', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00'
];

export default function TeacherTimetablePage() {
  const [selectedDay, setSelectedDay] = useState<string | null>(null);

  const generateTimetableGrid = () => {
    const grid: { [key: string]: any } = {};
    
    daysOfWeek.forEach(day => {
      timeSlots.forEach(time => {
        const key = `${day}-${time}`;
        const entry = mockTimetable.find(
          item => item.day === day && item.start_time === time
        );
        grid[key] = entry || null;
      });
    });

    return grid;
  };

  const timetableGrid = generateTimetableGrid();

  const getTodaysClasses = () => {
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
    return mockTimetable.filter(item => item.day === today);
  };

  const getWeeklyStats = () => {
    const totalClasses = mockTimetable.length;
    const uniqueClasses = new Set(mockTimetable.map(item => item.class)).size;
    const totalStudents = mockTimetable.reduce((sum, item) => sum + item.students, 0);
    
    return {
      totalClasses,
      uniqueClasses,
      totalStudents,
      averageClassSize: Math.round(totalStudents / totalClasses),
    };
  };

  const todaysClasses = getTodaysClasses();
  const weeklyStats = getWeeklyStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Timetable</h1>
        <p className="text-gray-600">View your weekly class schedule and upcoming sessions</p>
      </div>

      {/* Weekly Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Weekly Classes</p>
                <p className="text-2xl font-bold text-blue-600">{weeklyStats.totalClasses}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-50">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Different Classes</p>
                <p className="text-2xl font-bold text-green-600">{weeklyStats.uniqueClasses}</p>
              </div>
              <div className="p-3 rounded-full bg-green-50">
                <BookOpen className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-purple-600">{weeklyStats.totalStudents}</p>
              </div>
              <div className="p-3 rounded-full bg-purple-50">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Class Size</p>
                <p className="text-2xl font-bold text-orange-600">{weeklyStats.averageClassSize}</p>
              </div>
              <div className="p-3 rounded-full bg-orange-50">
                <Users className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Today's Classes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>Today's Classes</span>
          </CardTitle>
          <CardDescription>
            Your schedule for today ({new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })})
          </CardDescription>
        </CardHeader>
        <CardContent>
          {todaysClasses.length > 0 ? (
            <div className="space-y-4">
              {todaysClasses.map((cls) => (
                <div key={cls.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium text-gray-900">{cls.class}</p>
                      <p className="text-sm text-gray-600">{cls.subject}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-6 text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{cls.time}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-4 w-4" />
                      <span>{cls.room}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{cls.students} students</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No classes scheduled for today</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Weekly Timetable Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Weekly Timetable</span>
          </CardTitle>
          <CardDescription>
            Your complete weekly schedule
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-20">Time</TableHead>
                  {daysOfWeek.map(day => (
                    <TableHead key={day} className="text-center min-w-40">
                      {day}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {timeSlots.map(time => (
                  <TableRow key={time}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <span>{time}</span>
                      </div>
                    </TableCell>
                    {daysOfWeek.map(day => {
                      const entry = timetableGrid[`${day}-${time}`];
                      return (
                        <TableCell key={`${day}-${time}`} className="text-center p-2">
                          {entry ? (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-sm">
                              <div className="font-medium text-green-900 mb-1">
                                {entry.class}
                              </div>
                              <div className="text-green-700 text-xs mb-1">
                                {entry.subject}
                              </div>
                              <div className="text-green-600 text-xs flex items-center justify-center space-x-2">
                                <span>{entry.room}</span>
                                <span>•</span>
                                <span>{entry.students}</span>
                              </div>
                            </div>
                          ) : (
                            <div className="text-gray-400 text-sm py-4">-</div>
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Class Details */}
      <Card>
        <CardHeader>
          <CardTitle>Class Details</CardTitle>
          <CardDescription>Detailed information about your assigned classes</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Day</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Class</TableHead>
                <TableHead>Subject</TableHead>
                <TableHead>Room</TableHead>
                <TableHead>Students</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockTimetable.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell className="font-medium">{entry.day}</TableCell>
                  <TableCell>{entry.time}</TableCell>
                  <TableCell>{entry.class}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{entry.subject}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span>{entry.room}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span>{entry.students}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
