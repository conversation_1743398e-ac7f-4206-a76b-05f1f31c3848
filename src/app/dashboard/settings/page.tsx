'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Settings, School, Users, Bell, Database, Shield } from 'lucide-react';
import { toast } from 'sonner';

export default function SettingsPage() {
  const [schoolSettings, setSchoolSettings] = useState({
    name: 'Greenwood Elementary School',
    address: '123 Education Street, Learning City, LC 12345',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'www.greenwood.edu',
    academic_year: '2023-2024',
    timezone: 'America/New_York',
  });

  const [systemSettings, setSystemSettings] = useState({
    max_students_per_class: 30,
    attendance_grace_period: 15,
    fee_due_reminder_days: 7,
    backup_frequency: 'daily',
    maintenance_mode: false,
  });

  const [notificationSettings, setNotificationSettings] = useState({
    email_notifications: true,
    sms_notifications: false,
    push_notifications: true,
    attendance_alerts: true,
    fee_reminders: true,
    system_updates: true,
  });

  const handleSaveSchoolSettings = () => {
    // In a real app, this would save to the database
    toast.success('School settings saved successfully');
  };

  const handleSaveSystemSettings = () => {
    // In a real app, this would save to the database
    toast.success('System settings saved successfully');
  };

  const handleSaveNotificationSettings = () => {
    // In a real app, this would save to the database
    toast.success('Notification settings saved successfully');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Manage system configuration and preferences</p>
      </div>

      <Tabs defaultValue="school" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="school" className="flex items-center space-x-2">
            <School className="h-4 w-4" />
            <span>School</span>
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>System</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center space-x-2">
            <Bell className="h-4 w-4" />
            <span>Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Security</span>
          </TabsTrigger>
        </TabsList>

        {/* School Settings */}
        <TabsContent value="school">
          <Card>
            <CardHeader>
              <CardTitle>School Information</CardTitle>
              <CardDescription>
                Basic information about your school
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="school_name">School Name</Label>
                  <Input
                    id="school_name"
                    value={schoolSettings.name}
                    onChange={(e) => setSchoolSettings(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="academic_year">Academic Year</Label>
                  <Select value={schoolSettings.academic_year} onValueChange={(value) => setSchoolSettings(prev => ({ ...prev, academic_year: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2023-2024">2023-2024</SelectItem>
                      <SelectItem value="2024-2025">2024-2025</SelectItem>
                      <SelectItem value="2025-2026">2025-2026</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={schoolSettings.address}
                  onChange={(e) => setSchoolSettings(prev => ({ ...prev, address: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={schoolSettings.phone}
                    onChange={(e) => setSchoolSettings(prev => ({ ...prev, phone: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={schoolSettings.email}
                    onChange={(e) => setSchoolSettings(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={schoolSettings.website}
                    onChange={(e) => setSchoolSettings(prev => ({ ...prev, website: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select value={schoolSettings.timezone} onValueChange={(value) => setSchoolSettings(prev => ({ ...prev, timezone: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="America/New_York">Eastern Time</SelectItem>
                      <SelectItem value="America/Chicago">Central Time</SelectItem>
                      <SelectItem value="America/Denver">Mountain Time</SelectItem>
                      <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button onClick={handleSaveSchoolSettings}>
                Save School Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Settings */}
        <TabsContent value="system">
          <Card>
            <CardHeader>
              <CardTitle>System Configuration</CardTitle>
              <CardDescription>
                Configure system-wide settings and limits
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="max_students">Max Students per Class</Label>
                  <Input
                    id="max_students"
                    type="number"
                    min="1"
                    max="50"
                    value={systemSettings.max_students_per_class}
                    onChange={(e) => setSystemSettings(prev => ({ ...prev, max_students_per_class: parseInt(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="grace_period">Attendance Grace Period (minutes)</Label>
                  <Input
                    id="grace_period"
                    type="number"
                    min="0"
                    max="60"
                    value={systemSettings.attendance_grace_period}
                    onChange={(e) => setSystemSettings(prev => ({ ...prev, attendance_grace_period: parseInt(e.target.value) }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="reminder_days">Fee Due Reminder (days before)</Label>
                  <Input
                    id="reminder_days"
                    type="number"
                    min="1"
                    max="30"
                    value={systemSettings.fee_due_reminder_days}
                    onChange={(e) => setSystemSettings(prev => ({ ...prev, fee_due_reminder_days: parseInt(e.target.value) }))}
                  />
                </div>
                <div>
                  <Label htmlFor="backup_frequency">Backup Frequency</Label>
                  <Select value={systemSettings.backup_frequency} onValueChange={(value) => setSystemSettings(prev => ({ ...prev, backup_frequency: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="maintenance_mode"
                  checked={systemSettings.maintenance_mode}
                  onChange={(e) => setSystemSettings(prev => ({ ...prev, maintenance_mode: e.target.checked }))}
                  className="rounded"
                />
                <Label htmlFor="maintenance_mode">Maintenance Mode</Label>
                {systemSettings.maintenance_mode && (
                  <Badge variant="destructive">Active</Badge>
                )}
              </div>

              <Button onClick={handleSaveSystemSettings}>
                Save System Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Configure how and when notifications are sent
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="email_notifications">Email Notifications</Label>
                    <p className="text-sm text-gray-600">Receive notifications via email</p>
                  </div>
                  <input
                    type="checkbox"
                    id="email_notifications"
                    checked={notificationSettings.email_notifications}
                    onChange={(e) => setNotificationSettings(prev => ({ ...prev, email_notifications: e.target.checked }))}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="sms_notifications">SMS Notifications</Label>
                    <p className="text-sm text-gray-600">Receive notifications via SMS</p>
                  </div>
                  <input
                    type="checkbox"
                    id="sms_notifications"
                    checked={notificationSettings.sms_notifications}
                    onChange={(e) => setNotificationSettings(prev => ({ ...prev, sms_notifications: e.target.checked }))}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="push_notifications">Push Notifications</Label>
                    <p className="text-sm text-gray-600">Receive browser push notifications</p>
                  </div>
                  <input
                    type="checkbox"
                    id="push_notifications"
                    checked={notificationSettings.push_notifications}
                    onChange={(e) => setNotificationSettings(prev => ({ ...prev, push_notifications: e.target.checked }))}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="attendance_alerts">Attendance Alerts</Label>
                    <p className="text-sm text-gray-600">Get notified about attendance issues</p>
                  </div>
                  <input
                    type="checkbox"
                    id="attendance_alerts"
                    checked={notificationSettings.attendance_alerts}
                    onChange={(e) => setNotificationSettings(prev => ({ ...prev, attendance_alerts: e.target.checked }))}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="fee_reminders">Fee Reminders</Label>
                    <p className="text-sm text-gray-600">Send reminders for pending fees</p>
                  </div>
                  <input
                    type="checkbox"
                    id="fee_reminders"
                    checked={notificationSettings.fee_reminders}
                    onChange={(e) => setNotificationSettings(prev => ({ ...prev, fee_reminders: e.target.checked }))}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="system_updates">System Updates</Label>
                    <p className="text-sm text-gray-600">Get notified about system updates</p>
                  </div>
                  <input
                    type="checkbox"
                    id="system_updates"
                    checked={notificationSettings.system_updates}
                    onChange={(e) => setNotificationSettings(prev => ({ ...prev, system_updates: e.target.checked }))}
                    className="rounded"
                  />
                </div>
              </div>

              <Button onClick={handleSaveNotificationSettings}>
                Save Notification Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Security & Privacy</CardTitle>
              <CardDescription>
                Manage security settings and data privacy
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Demo Mode</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    This application is currently running in demo mode. No real data is stored or processed.
                  </p>
                  <Badge variant="secondary">Demo Mode Active</Badge>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Data Backup</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Regular backups ensure your data is safe and recoverable.
                  </p>
                  <Button variant="outline">
                    <Database className="h-4 w-4 mr-2" />
                    Create Backup
                  </Button>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">User Management</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Manage user roles and permissions.
                  </p>
                  <Button variant="outline">
                    <Users className="h-4 w-4 mr-2" />
                    Manage Users
                  </Button>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">System Logs</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    View system activity and audit logs.
                  </p>
                  <Button variant="outline">
                    View Logs
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
