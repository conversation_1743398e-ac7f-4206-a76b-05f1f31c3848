'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Calendar, Download, FileText } from 'lucide-react';
import { Attendance } from '@/lib/types';
import { toast } from 'sonner';

// Mock data for demonstration
const mockAttendance: Attendance[] = [
  {
    id: '1',
    student_id: '1',
    class_id: 'class1',
    date: '2024-01-15',
    status: 'present',
    marked_by: 'teacher1',
    created_at: '2024-01-15T09:00:00Z',
    updated_at: '2024-01-15T09:00:00Z',
  },
  {
    id: '2',
    student_id: '2',
    class_id: 'class1',
    date: '2024-01-15',
    status: 'absent',
    marked_by: 'teacher1',
    notes: 'Sick leave',
    created_at: '2024-01-15T09:00:00Z',
    updated_at: '2024-01-15T09:00:00Z',
  },
];

const mockStudents = [
  { id: '1', name: 'John Doe', student_id: 'STU001' },
  { id: '2', name: 'Jane Smith', student_id: 'STU002' },
  { id: '3', name: 'Mike Johnson', student_id: 'STU003' },
  { id: '4', name: 'Sarah Wilson', student_id: 'STU004' },
];

export default function AttendancePage() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedClass, setSelectedClass] = useState('class1');
  const [attendance, setAttendance] = useState<Record<string, 'present' | 'absent' | 'late' | 'excused'>>({});

  const handleAttendanceChange = (studentId: string, status: 'present' | 'absent' | 'late' | 'excused') => {
    setAttendance(prev => ({
      ...prev,
      [studentId]: status
    }));
  };

  const handleSubmitAttendance = () => {
    // In a real app, this would save to the database
    toast.success('Attendance marked successfully');
  };

  const handleExportReport = () => {
    // In a real app, this would generate and download a report
    toast.success('Attendance report exported successfully');
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      present: 'default',
      absent: 'destructive',
      late: 'secondary',
      excused: 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Attendance</h1>
          <p className="text-gray-600">Mark and manage student attendance</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExportReport}>
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            View Reports
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Mark Attendance</CardTitle>
          <CardDescription>Select date and class to mark attendance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="class">Class</Label>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="class1">Grade 1 - A</SelectItem>
                  <SelectItem value="class2">Grade 1 - B</SelectItem>
                  <SelectItem value="class3">Grade 2 - A</SelectItem>
                  <SelectItem value="class4">Grade 2 - B</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={handleSubmitAttendance} className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                Save Attendance
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Attendance Marking */}
      <Card>
        <CardHeader>
          <CardTitle>Students - {selectedClass}</CardTitle>
          <CardDescription>
            Mark attendance for {selectedDate}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockStudents.map((student) => (
                <TableRow key={student.id}>
                  <TableCell className="font-medium">{student.student_id}</TableCell>
                  <TableCell>{student.name}</TableCell>
                  <TableCell>
                    {attendance[student.id] ? getStatusBadge(attendance[student.id]) : (
                      <Badge variant="outline">Not Marked</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant={attendance[student.id] === 'present' ? 'default' : 'outline'}
                        onClick={() => handleAttendanceChange(student.id, 'present')}
                      >
                        Present
                      </Button>
                      <Button
                        size="sm"
                        variant={attendance[student.id] === 'absent' ? 'destructive' : 'outline'}
                        onClick={() => handleAttendanceChange(student.id, 'absent')}
                      >
                        Absent
                      </Button>
                      <Button
                        size="sm"
                        variant={attendance[student.id] === 'late' ? 'secondary' : 'outline'}
                        onClick={() => handleAttendanceChange(student.id, 'late')}
                      >
                        Late
                      </Button>
                      <Button
                        size="sm"
                        variant={attendance[student.id] === 'excused' ? 'outline' : 'outline'}
                        onClick={() => handleAttendanceChange(student.id, 'excused')}
                      >
                        Excused
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Attendance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Present</p>
                <p className="text-2xl font-bold text-green-600">
                  {Object.values(attendance).filter(status => status === 'present').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-50">
                <Calendar className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Absent</p>
                <p className="text-2xl font-bold text-red-600">
                  {Object.values(attendance).filter(status => status === 'absent').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-red-50">
                <Calendar className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Late</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {Object.values(attendance).filter(status => status === 'late').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-yellow-50">
                <Calendar className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Excused</p>
                <p className="text-2xl font-bold text-blue-600">
                  {Object.values(attendance).filter(status => status === 'excused').length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-blue-50">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
