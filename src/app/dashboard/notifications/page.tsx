'use client';

import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Send, Bell, Users, GraduationCap, User } from 'lucide-react';
import { Notification } from '@/lib/types';
import { toast } from 'sonner';

// Mock data for demonstration
const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'Parent-Teacher Meeting',
    message: 'Parent-teacher meeting scheduled for next Friday at 2 PM. Please confirm your attendance.',
    type: 'info',
    target_audience: 'parents',
    sent_by: 'admin',
    sent_at: '2024-01-15T10:00:00Z',
    read_by: ['user1', 'user2'],
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    title: 'Holiday Notice',
    message: 'School will be closed on Monday due to national holiday. Classes will resume on Tuesday.',
    type: 'warning',
    target_audience: 'all',
    sent_by: 'admin',
    sent_at: '2024-01-14T09:00:00Z',
    read_by: ['user1'],
    created_at: '2024-01-14T09:00:00Z',
    updated_at: '2024-01-14T09:00:00Z',
  },
  {
    id: '3',
    title: 'Exam Results Published',
    message: 'Mid-term exam results have been published. Students can check their results on the portal.',
    type: 'success',
    target_audience: 'students',
    sent_by: 'admin',
    sent_at: '2024-01-13T14:00:00Z',
    read_by: [],
    created_at: '2024-01-13T14:00:00Z',
    updated_at: '2024-01-13T14:00:00Z',
  },
];

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: 'info' as 'info' | 'warning' | 'success' | 'error',
    target_audience: 'all' as 'all' | 'teachers' | 'students' | 'parents',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const newNotification: Notification = {
      id: Date.now().toString(),
      ...formData,
      sent_by: 'admin',
      sent_at: new Date().toISOString(),
      read_by: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    setNotifications(prev => [newNotification, ...prev]);
    toast.success('Notification sent successfully');
    
    setFormData({
      title: '',
      message: '',
      type: 'info',
      target_audience: 'all',
    });
    setIsDialogOpen(false);
  };

  const getTypeBadge = (type: string) => {
    const variants = {
      info: 'default',
      warning: 'secondary',
      success: 'default',
      error: 'destructive'
    } as const;

    const colors = {
      info: 'bg-blue-50 text-blue-700',
      warning: 'bg-yellow-50 text-yellow-700',
      success: 'bg-green-50 text-green-700',
      error: 'bg-red-50 text-red-700'
    };

    return (
      <Badge variant={variants[type as keyof typeof variants]} className={colors[type as keyof typeof colors]}>
        {type}
      </Badge>
    );
  };

  const getAudienceIcon = (audience: string) => {
    switch (audience) {
      case 'teachers':
        return <GraduationCap className="h-4 w-4" />;
      case 'students':
        return <User className="h-4 w-4" />;
      case 'parents':
        return <Users className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
          <p className="text-gray-600">Send and manage school notifications</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Send Notification
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Send New Notification</DialogTitle>
              <DialogDescription>
                Create and send a notification to selected audience
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter notification title"
                  required
                />
              </div>

              <div>
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="Enter notification message"
                  rows={4}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="type">Type</Label>
                  <Select value={formData.type} onValueChange={(value: any) => setFormData(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="warning">Warning</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="audience">Target Audience</Label>
                  <Select value={formData.target_audience} onValueChange={(value: any) => setFormData(prev => ({ ...prev, target_audience: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="teachers">Teachers</SelectItem>
                      <SelectItem value="students">Students</SelectItem>
                      <SelectItem value="parents">Parents</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  <Send className="h-4 w-4 mr-2" />
                  Send Notification
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sent</p>
                <p className="text-2xl font-bold text-gray-900">{notifications.length}</p>
              </div>
              <Bell className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">To Teachers</p>
                <p className="text-2xl font-bold text-green-600">
                  {notifications.filter(n => n.target_audience === 'teachers' || n.target_audience === 'all').length}
                </p>
              </div>
              <GraduationCap className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">To Students</p>
                <p className="text-2xl font-bold text-purple-600">
                  {notifications.filter(n => n.target_audience === 'students' || n.target_audience === 'all').length}
                </p>
              </div>
              <User className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">To Parents</p>
                <p className="text-2xl font-bold text-orange-600">
                  {notifications.filter(n => n.target_audience === 'parents' || n.target_audience === 'all').length}
                </p>
              </div>
              <Users className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Notifications</CardTitle>
          <CardDescription>
            All sent notifications and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div key={notification.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold text-gray-900">{notification.title}</h3>
                      {getTypeBadge(notification.type)}
                    </div>
                    <p className="text-gray-600 text-sm mb-3">{notification.message}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        {getAudienceIcon(notification.target_audience)}
                        <span className="capitalize">{notification.target_audience}</span>
                      </div>
                      <span>•</span>
                      <span>{formatDate(notification.sent_at)}</span>
                      <span>•</span>
                      <span>{notification.read_by.length} read</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
