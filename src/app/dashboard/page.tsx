'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, GraduationCap, BookOpen, Calendar, DollarSign, TrendingUp } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { DashboardStats, AttendanceReport } from '@/lib/types';

// Mock data for demonstration
const mockStats: DashboardStats = {
  total_students: 1250,
  total_teachers: 85,
  total_classes: 42,
  attendance_rate: 92.5,
  pending_fees: 15000,
  recent_notifications: 8,
};

const mockAttendanceData: AttendanceReport[] = [
  { date: '2024-01-15', present: 1150, absent: 100, late: 25, total: 1250 },
  { date: '2024-01-16', present: 1180, absent: 70, late: 15, total: 1250 },
  { date: '2024-01-17', present: 1200, absent: 50, late: 20, total: 1250 },
  { date: '2024-01-18', present: 1160, absent: 90, late: 30, total: 1250 },
  { date: '2024-01-19', present: 1190, absent: 60, late: 18, total: 1250 },
];

const gradeDistribution = [
  { grade: 'Grade 1', students: 180, fill: '#8884d8' },
  { grade: 'Grade 2', students: 165, fill: '#82ca9d' },
  { grade: 'Grade 3', students: 155, fill: '#ffc658' },
  { grade: 'Grade 4', students: 170, fill: '#ff7300' },
  { grade: 'Grade 5', students: 160, fill: '#00ff00' },
  { grade: 'Grade 6', students: 145, fill: '#ff0000' },
  { grade: 'Grade 7', students: 140, fill: '#0000ff' },
  { grade: 'Grade 8', students: 135, fill: '#ff00ff' },
];

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>(mockStats);
  const [attendanceData, setAttendanceData] = useState<AttendanceReport[]>(mockAttendanceData);

  const statCards = [
    {
      title: 'Total Students',
      value: stats.total_students.toLocaleString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Total Teachers',
      value: stats.total_teachers.toString(),
      icon: GraduationCap,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Total Classes',
      value: stats.total_classes.toString(),
      icon: BookOpen,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Attendance Rate',
      value: `${stats.attendance_rate}%`,
      icon: Calendar,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Pending Fees',
      value: `$${stats.pending_fees.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Notifications',
      value: stats.recent_notifications.toString(),
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to the School Admin Dashboard</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                </div>
                <div className={`p-3 rounded-full ${card.bgColor}`}>
                  <card.icon className={`h-6 w-6 ${card.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Attendance Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Attendance</CardTitle>
            <CardDescription>Student attendance over the past week</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={attendanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="present" fill="#10b981" name="Present" />
                <Bar dataKey="absent" fill="#ef4444" name="Absent" />
                <Bar dataKey="late" fill="#f59e0b" name="Late" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Grade Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Student Distribution by Grade</CardTitle>
            <CardDescription>Number of students in each grade</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={gradeDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ grade, students }) => `${grade}: ${students}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="students"
                >
                  {gradeDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest updates and notifications</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Badge variant="secondary">New</Badge>
              <span className="text-sm">5 new students enrolled in Grade 3</span>
              <span className="text-xs text-gray-500">2 hours ago</span>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="outline">Update</Badge>
              <span className="text-sm">Attendance marked for all classes</span>
              <span className="text-xs text-gray-500">4 hours ago</span>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant="destructive">Alert</Badge>
              <span className="text-sm">15 students have pending fee payments</span>
              <span className="text-xs text-gray-500">1 day ago</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
