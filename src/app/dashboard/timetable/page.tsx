'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Plus, Clock, Calendar } from 'lucide-react';
import { Timetable } from '@/lib/types';
import { toast } from 'sonner';

// Mock data for demonstration
const mockTimetable: Timetable[] = [
  {
    id: '1',
    class_id: 'class1',
    subject_id: 'math',
    teacher_id: '1',
    day_of_week: 1, // Monday
    start_time: '09:00',
    end_time: '10:00',
    room_number: 'Room 101',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    class_id: 'class1',
    subject_id: 'english',
    teacher_id: '2',
    day_of_week: 1, // Monday
    start_time: '10:00',
    end_time: '11:00',
    room_number: 'Room 102',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

const mockClasses = [
  { id: 'class1', name: 'Grade 1 - A' },
  { id: 'class2', name: 'Grade 1 - B' },
  { id: 'class3', name: 'Grade 2 - A' },
];

const mockSubjects = [
  { id: 'math', name: 'Mathematics' },
  { id: 'english', name: 'English' },
  { id: 'science', name: 'Science' },
  { id: 'history', name: 'History' },
];

const mockTeachers = [
  { id: '1', name: 'Sarah Johnson' },
  { id: '2', name: 'Michael Brown' },
  { id: '3', name: 'Emily Davis' },
];

const daysOfWeek = [
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
];

const timeSlots = [
  '08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'
];

export default function TimetablePage() {
  const [selectedClass, setSelectedClass] = useState('class1');
  const [timetable, setTimetable] = useState<Timetable[]>(mockTimetable);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    subject_id: '',
    teacher_id: '',
    day_of_week: 1,
    start_time: '',
    end_time: '',
    room_number: '',
  });

  const filteredTimetable = timetable.filter(item => item.class_id === selectedClass);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const newEntry: Timetable = {
      id: Date.now().toString(),
      class_id: selectedClass,
      ...formData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    setTimetable(prev => [...prev, newEntry]);
    toast.success('Timetable entry added successfully');
    
    setFormData({
      subject_id: '',
      teacher_id: '',
      day_of_week: 1,
      start_time: '',
      end_time: '',
      room_number: '',
    });
    setIsDialogOpen(false);
  };

  const getSubjectName = (subjectId: string) => {
    return mockSubjects.find(s => s.id === subjectId)?.name || 'Unknown';
  };

  const getTeacherName = (teacherId: string) => {
    return mockTeachers.find(t => t.id === teacherId)?.name || 'Unknown';
  };

  const getDayName = (dayNumber: number) => {
    return daysOfWeek.find(d => d.value === dayNumber)?.label || 'Unknown';
  };

  const generateTimetableGrid = () => {
    const grid: { [key: string]: Timetable | null } = {};
    
    daysOfWeek.forEach(day => {
      timeSlots.forEach(time => {
        const key = `${day.value}-${time}`;
        const entry = filteredTimetable.find(
          item => item.day_of_week === day.value && item.start_time === time
        );
        grid[key] = entry || null;
      });
    });

    return grid;
  };

  const timetableGrid = generateTimetableGrid();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Timetable Management</h1>
          <p className="text-gray-600">Manage class schedules and time slots</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Time Slot
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Timetable Entry</DialogTitle>
              <DialogDescription>
                Create a new time slot for the selected class
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="subject">Subject</Label>
                  <Select value={formData.subject_id} onValueChange={(value) => setFormData(prev => ({ ...prev, subject_id: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockSubjects.map(subject => (
                        <SelectItem key={subject.id} value={subject.id}>
                          {subject.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="teacher">Teacher</Label>
                  <Select value={formData.teacher_id} onValueChange={(value) => setFormData(prev => ({ ...prev, teacher_id: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select teacher" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockTeachers.map(teacher => (
                        <SelectItem key={teacher.id} value={teacher.id}>
                          {teacher.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="day">Day</Label>
                  <Select value={formData.day_of_week.toString()} onValueChange={(value) => setFormData(prev => ({ ...prev, day_of_week: parseInt(value) }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {daysOfWeek.map(day => (
                        <SelectItem key={day.value} value={day.value.toString()}>
                          {day.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="start_time">Start Time</Label>
                  <Select value={formData.start_time} onValueChange={(value) => setFormData(prev => ({ ...prev, start_time: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Start" />
                    </SelectTrigger>
                    <SelectContent>
                      {timeSlots.map(time => (
                        <SelectItem key={time} value={time}>
                          {time}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="end_time">End Time</Label>
                  <Select value={formData.end_time} onValueChange={(value) => setFormData(prev => ({ ...prev, end_time: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="End" />
                    </SelectTrigger>
                    <SelectContent>
                      {timeSlots.map(time => (
                        <SelectItem key={time} value={time}>
                          {time}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="room_number">Room Number</Label>
                <Input
                  id="room_number"
                  value={formData.room_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, room_number: e.target.value }))}
                  placeholder="e.g., Room 101"
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  Add Entry
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Class Selection */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <Label htmlFor="class-select">Select Class:</Label>
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {mockClasses.map(cls => (
                  <SelectItem key={cls.id} value={cls.id}>
                    {cls.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Timetable Grid */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Weekly Timetable - {mockClasses.find(c => c.id === selectedClass)?.name}</span>
          </CardTitle>
          <CardDescription>
            Class schedule for the week
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-20">Time</TableHead>
                  {daysOfWeek.map(day => (
                    <TableHead key={day.value} className="text-center min-w-32">
                      {day.label}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {timeSlots.map(time => (
                  <TableRow key={time}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <span>{time}</span>
                      </div>
                    </TableCell>
                    {daysOfWeek.map(day => {
                      const entry = timetableGrid[`${day.value}-${time}`];
                      return (
                        <TableCell key={`${day.value}-${time}`} className="text-center">
                          {entry ? (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 text-sm">
                              <div className="font-medium text-blue-900">
                                {getSubjectName(entry.subject_id)}
                              </div>
                              <div className="text-blue-600 text-xs">
                                {getTeacherName(entry.teacher_id)}
                              </div>
                              {entry.room_number && (
                                <div className="text-blue-500 text-xs">
                                  {entry.room_number}
                                </div>
                              )}
                            </div>
                          ) : (
                            <div className="text-gray-400 text-sm">-</div>
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Periods</p>
                <p className="text-2xl font-bold text-blue-600">{filteredTimetable.length}</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Subjects</p>
                <p className="text-2xl font-bold text-green-600">
                  {new Set(filteredTimetable.map(t => t.subject_id)).size}
                </p>
              </div>
              <Clock className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Teachers</p>
                <p className="text-2xl font-bold text-purple-600">
                  {new Set(filteredTimetable.map(t => t.teacher_id)).size}
                </p>
              </div>
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
