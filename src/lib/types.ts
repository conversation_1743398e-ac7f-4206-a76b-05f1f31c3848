// Database Types
export interface User {
  id: string;
  email: string;
  role: 'admin' | 'teacher' | 'student' | 'parent';
  created_at: string;
  updated_at: string;
}

export interface Student {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  address: string;
  phone: string;
  parent_email: string;
  class_id: string;
  admission_date: string;
  student_id: string;
  status: 'active' | 'inactive' | 'graduated';
  created_at: string;
  updated_at: string;
}

export interface Teacher {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  qualification: string;
  experience_years: number;
  salary: number;
  hire_date: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Class {
  id: string;
  name: string;
  section: string;
  grade: number;
  teacher_id: string;
  academic_year: string;
  max_students: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface Subject {
  id: string;
  name: string;
  code: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface Attendance {
  id: string;
  student_id: string;
  class_id: string;
  date: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  marked_by: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Timetable {
  id: string;
  class_id: string;
  subject_id: string;
  teacher_id: string;
  day_of_week: number; // 0-6 (Sunday-Saturday)
  start_time: string;
  end_time: string;
  room_number?: string;
  created_at: string;
  updated_at: string;
}

export interface Fee {
  id: string;
  student_id: string;
  fee_type: 'tuition' | 'transport' | 'library' | 'lab' | 'other';
  amount: number;
  due_date: string;
  paid_date?: string;
  status: 'pending' | 'paid' | 'overdue';
  payment_method?: 'cash' | 'card' | 'bank_transfer' | 'online';
  receipt_number?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  target_audience: 'all' | 'teachers' | 'students' | 'parents';
  sent_by: string;
  sent_at: string;
  read_by: string[]; // Array of user IDs who have read the notification
  created_at: string;
  updated_at: string;
}

// Form Types
export interface StudentFormData {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  address: string;
  phone: string;
  parent_email: string;
  class_id: string;
  student_id: string;
}

export interface TeacherFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  qualification: string;
  experience_years: number;
  salary: number;
  hire_date: string;
}

export interface ClassFormData {
  name: string;
  section: string;
  grade: number;
  teacher_id: string;
  academic_year: string;
  max_students: number;
}

// Dashboard Types
export interface DashboardStats {
  total_students: number;
  total_teachers: number;
  total_classes: number;
  attendance_rate: number;
  pending_fees: number;
  recent_notifications: number;
}

export interface AttendanceReport {
  date: string;
  present: number;
  absent: number;
  late: number;
  total: number;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
