import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database helper functions
export const db = {
  // Students
  students: {
    getAll: () => supabase.from('students').select('*'),
    getById: (id: string) => supabase.from('students').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('students').insert(data),
    update: (id: string, data: any) => supabase.from('students').update(data).eq('id', id),
    delete: (id: string) => supabase.from('students').delete().eq('id', id),
  },

  // Teachers
  teachers: {
    getAll: () => supabase.from('teachers').select('*'),
    getById: (id: string) => supabase.from('teachers').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('teachers').insert(data),
    update: (id: string, data: any) => supabase.from('teachers').update(data).eq('id', id),
    delete: (id: string) => supabase.from('teachers').delete().eq('id', id),
  },

  // Classes
  classes: {
    getAll: () => supabase.from('classes').select('*'),
    getById: (id: string) => supabase.from('classes').select('*').eq('id', id).single(),
    create: (data: any) => supabase.from('classes').insert(data),
    update: (id: string, data: any) => supabase.from('classes').update(data).eq('id', id),
    delete: (id: string) => supabase.from('classes').delete().eq('id', id),
  },

  // Attendance
  attendance: {
    getAll: () => supabase.from('attendance').select('*'),
    getByDate: (date: string) => supabase.from('attendance').select('*').eq('date', date),
    getByStudent: (studentId: string) => supabase.from('attendance').select('*').eq('student_id', studentId),
    create: (data: any) => supabase.from('attendance').insert(data),
    update: (id: string, data: any) => supabase.from('attendance').update(data).eq('id', id),
  },

  // Timetable
  timetable: {
    getAll: () => supabase.from('timetable').select('*'),
    getByClass: (classId: string) => supabase.from('timetable').select('*').eq('class_id', classId),
    create: (data: any) => supabase.from('timetable').insert(data),
    update: (id: string, data: any) => supabase.from('timetable').update(data).eq('id', id),
    delete: (id: string) => supabase.from('timetable').delete().eq('id', id),
  },

  // Fees
  fees: {
    getAll: () => supabase.from('fees').select('*'),
    getByStudent: (studentId: string) => supabase.from('fees').select('*').eq('student_id', studentId),
    getPending: () => supabase.from('fees').select('*').eq('status', 'pending'),
    create: (data: any) => supabase.from('fees').insert(data),
    update: (id: string, data: any) => supabase.from('fees').update(data).eq('id', id),
  },

  // Notifications
  notifications: {
    getAll: () => supabase.from('notifications').select('*'),
    getRecent: (limit: number = 10) => supabase.from('notifications').select('*').order('created_at', { ascending: false }).limit(limit),
    create: (data: any) => supabase.from('notifications').insert(data),
    markAsRead: (id: string, userId: string) => {
      // This would need custom logic to update the read_by array
      return supabase.rpc('mark_notification_read', { notification_id: id, user_id: userId });
    },
  },
};

// Auth helper functions
export const auth = {
  signIn: (email: string, password: string) => supabase.auth.signInWithPassword({ email, password }),
  signOut: () => supabase.auth.signOut(),
  getUser: () => supabase.auth.getUser(),
  getSession: () => supabase.auth.getSession(),
};
