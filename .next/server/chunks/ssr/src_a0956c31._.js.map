{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/app/dashboard/teachers/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { Badge } from '@/components/ui/badge';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Plus, Search, Edit, Trash2, Eye } from 'lucide-react';\nimport { Teacher, TeacherFormData } from '@/lib/types';\nimport { toast } from 'sonner';\n\n// Mock data for demonstration\nconst mockTeachers: Teacher[] = [\n  {\n    id: '1',\n    user_id: 'user1',\n    first_name: '<PERSON>',\n    last_name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '555-0101',\n    address: '123 Teacher St, City',\n    date_of_birth: '1985-03-15',\n    gender: 'female',\n    qualification: 'M.Ed in Mathematics',\n    experience_years: 8,\n    salary: 55000,\n    hire_date: '2020-08-15',\n    status: 'active',\n    created_at: '2020-08-15T00:00:00Z',\n    updated_at: '2020-08-15T00:00:00Z',\n  },\n  {\n    id: '2',\n    user_id: 'user2',\n    first_name: 'Michael',\n    last_name: 'Brown',\n    email: '<EMAIL>',\n    phone: '555-0102',\n    address: '456 Education Ave, City',\n    date_of_birth: '1982-07-22',\n    gender: 'male',\n    qualification: 'B.Sc in Physics, B.Ed',\n    experience_years: 12,\n    salary: 62000,\n    hire_date: '2018-01-10',\n    status: 'active',\n    created_at: '2018-01-10T00:00:00Z',\n    updated_at: '2018-01-10T00:00:00Z',\n  },\n];\n\nexport default function TeachersPage() {\n  const [teachers, setTeachers] = useState<Teacher[]>(mockTeachers);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [editingTeacher, setEditingTeacher] = useState<Teacher | null>(null);\n  const [formData, setFormData] = useState<TeacherFormData>({\n    first_name: '',\n    last_name: '',\n    email: '',\n    phone: '',\n    address: '',\n    date_of_birth: '',\n    gender: 'male',\n    qualification: '',\n    experience_years: 0,\n    salary: 0,\n    hire_date: '',\n  });\n\n  const filteredTeachers = teachers.filter(teacher =>\n    `${teacher.first_name} ${teacher.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    teacher.email.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (editingTeacher) {\n      // Update existing teacher\n      setTeachers(prev => prev.map(teacher => \n        teacher.id === editingTeacher.id \n          ? { ...teacher, ...formData, updated_at: new Date().toISOString() }\n          : teacher\n      ));\n      toast.success('Teacher updated successfully');\n    } else {\n      // Add new teacher\n      const newTeacher: Teacher = {\n        id: Date.now().toString(),\n        user_id: `user${Date.now()}`,\n        ...formData,\n        status: 'active',\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      };\n      setTeachers(prev => [...prev, newTeacher]);\n      toast.success('Teacher added successfully');\n    }\n\n    resetForm();\n  };\n\n  const resetForm = () => {\n    setFormData({\n      first_name: '',\n      last_name: '',\n      email: '',\n      phone: '',\n      address: '',\n      date_of_birth: '',\n      gender: 'male',\n      qualification: '',\n      experience_years: 0,\n      salary: 0,\n      hire_date: '',\n    });\n    setEditingTeacher(null);\n    setIsDialogOpen(false);\n  };\n\n  const handleEdit = (teacher: Teacher) => {\n    setEditingTeacher(teacher);\n    setFormData({\n      first_name: teacher.first_name,\n      last_name: teacher.last_name,\n      email: teacher.email,\n      phone: teacher.phone,\n      address: teacher.address,\n      date_of_birth: teacher.date_of_birth,\n      gender: teacher.gender,\n      qualification: teacher.qualification,\n      experience_years: teacher.experience_years,\n      salary: teacher.salary,\n      hire_date: teacher.hire_date,\n    });\n    setIsDialogOpen(true);\n  };\n\n  const handleDelete = (id: string) => {\n    setTeachers(prev => prev.filter(teacher => teacher.id !== id));\n    toast.success('Teacher deleted successfully');\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Teachers</h1>\n          <p className=\"text-gray-600\">Manage teacher profiles and assignments</p>\n        </div>\n        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\n          <DialogTrigger asChild>\n            <Button onClick={() => resetForm()}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Teacher\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n            <DialogHeader>\n              <DialogTitle>{editingTeacher ? 'Edit Teacher' : 'Add New Teacher'}</DialogTitle>\n              <DialogDescription>\n                {editingTeacher ? 'Update teacher information' : 'Enter teacher details to add them to the system'}\n              </DialogDescription>\n            </DialogHeader>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"first_name\">First Name</Label>\n                  <Input\n                    id=\"first_name\"\n                    value={formData.first_name}\n                    onChange={(e) => setFormData(prev => ({ ...prev, first_name: e.target.value }))}\n                    required\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"last_name\">Last Name</Label>\n                  <Input\n                    id=\"last_name\"\n                    value={formData.last_name}\n                    onChange={(e) => setFormData(prev => ({ ...prev, last_name: e.target.value }))}\n                    required\n                  />\n                </div>\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"email\">Email</Label>\n                  <Input\n                    id=\"email\"\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}\n                    required\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"phone\">Phone</Label>\n                  <Input\n                    id=\"phone\"\n                    value={formData.phone}\n                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}\n                    required\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"date_of_birth\">Date of Birth</Label>\n                  <Input\n                    id=\"date_of_birth\"\n                    type=\"date\"\n                    value={formData.date_of_birth}\n                    onChange={(e) => setFormData(prev => ({ ...prev, date_of_birth: e.target.value }))}\n                    required\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"gender\">Gender</Label>\n                  <Select value={formData.gender} onValueChange={(value: 'male' | 'female' | 'other') => setFormData(prev => ({ ...prev, gender: value }))}>\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"male\">Male</SelectItem>\n                      <SelectItem value=\"female\">Female</SelectItem>\n                      <SelectItem value=\"other\">Other</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n\n              <div>\n                <Label htmlFor=\"address\">Address</Label>\n                <Input\n                  id=\"address\"\n                  value={formData.address}\n                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}\n                  required\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"qualification\">Qualification</Label>\n                <Input\n                  id=\"qualification\"\n                  value={formData.qualification}\n                  onChange={(e) => setFormData(prev => ({ ...prev, qualification: e.target.value }))}\n                  required\n                />\n              </div>\n\n              <div className=\"grid grid-cols-3 gap-4\">\n                <div>\n                  <Label htmlFor=\"experience_years\">Experience (Years)</Label>\n                  <Input\n                    id=\"experience_years\"\n                    type=\"number\"\n                    min=\"0\"\n                    value={formData.experience_years}\n                    onChange={(e) => setFormData(prev => ({ ...prev, experience_years: parseInt(e.target.value) || 0 }))}\n                    required\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"salary\">Salary</Label>\n                  <Input\n                    id=\"salary\"\n                    type=\"number\"\n                    min=\"0\"\n                    value={formData.salary}\n                    onChange={(e) => setFormData(prev => ({ ...prev, salary: parseInt(e.target.value) || 0 }))}\n                    required\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"hire_date\">Hire Date</Label>\n                  <Input\n                    id=\"hire_date\"\n                    type=\"date\"\n                    value={formData.hire_date}\n                    onChange={(e) => setFormData(prev => ({ ...prev, hire_date: e.target.value }))}\n                    required\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-2\">\n                <Button type=\"button\" variant=\"outline\" onClick={resetForm}>\n                  Cancel\n                </Button>\n                <Button type=\"submit\">\n                  {editingTeacher ? 'Update Teacher' : 'Add Teacher'}\n                </Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {/* Search */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n            <Input\n              placeholder=\"Search teachers by name or email...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Teachers Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Teachers List</CardTitle>\n          <CardDescription>\n            Total: {filteredTeachers.length} teachers\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Name</TableHead>\n                <TableHead>Email</TableHead>\n                <TableHead>Qualification</TableHead>\n                <TableHead>Experience</TableHead>\n                <TableHead>Status</TableHead>\n                <TableHead>Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredTeachers.map((teacher) => (\n                <TableRow key={teacher.id}>\n                  <TableCell className=\"font-medium\">{`${teacher.first_name} ${teacher.last_name}`}</TableCell>\n                  <TableCell>{teacher.email}</TableCell>\n                  <TableCell>{teacher.qualification}</TableCell>\n                  <TableCell>{teacher.experience_years} years</TableCell>\n                  <TableCell>\n                    <Badge variant={teacher.status === 'active' ? 'default' : 'secondary'}>\n                      {teacher.status}\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"flex space-x-2\">\n                      <Button variant=\"ghost\" size=\"sm\">\n                        <Eye className=\"h-4 w-4\" />\n                      </Button>\n                      <Button variant=\"ghost\" size=\"sm\" onClick={() => handleEdit(teacher)}>\n                        <Edit className=\"h-4 w-4\" />\n                      </Button>\n                      <Button variant=\"ghost\" size=\"sm\" onClick={() => handleDelete(teacher.id)}>\n                        <Trash2 className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AAbA;;;;;;;;;;;;;AAeA,8BAA8B;AAC9B,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,SAAS;QACT,YAAY;QACZ,WAAW;QACX,OAAO;QACP,OAAO;QACP,SAAS;QACT,eAAe;QACf,QAAQ;QACR,eAAe;QACf,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,YAAY;QACZ,WAAW;QACX,OAAO;QACP,OAAO;QACP,SAAS;QACT,eAAe;QACf,QAAQ;QACR,eAAe;QACf,kBAAkB;QAClB,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,YAAY;IACd;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,YAAY;QACZ,WAAW;QACX,OAAO;QACP,OAAO;QACP,SAAS;QACT,eAAe;QACf,QAAQ;QACR,eAAe;QACf,kBAAkB;QAClB,QAAQ;QACR,WAAW;IACb;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UACvC,GAAG,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1F,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG7D,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,gBAAgB;YAClB,0BAA0B;YAC1B,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAC3B,QAAQ,EAAE,KAAK,eAAe,EAAE,GAC5B;wBAAE,GAAG,OAAO;wBAAE,GAAG,QAAQ;wBAAE,YAAY,IAAI,OAAO,WAAW;oBAAG,IAChE;YAEN,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,OAAO;YACL,kBAAkB;YAClB,MAAM,aAAsB;gBAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,SAAS,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;gBAC5B,GAAG,QAAQ;gBACX,QAAQ;gBACR,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YACzC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QAEA;IACF;IAEA,MAAM,YAAY;QAChB,YAAY;YACV,YAAY;YACZ,WAAW;YACX,OAAO;YACP,OAAO;YACP,SAAS;YACT,eAAe;YACf,QAAQ;YACR,eAAe;YACf,kBAAkB;YAClB,QAAQ;YACR,WAAW;QACb;QACA,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB;QAClB,YAAY;YACV,YAAY,QAAQ,UAAU;YAC9B,WAAW,QAAQ,SAAS;YAC5B,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,KAAK;YACpB,SAAS,QAAQ,OAAO;YACxB,eAAe,QAAQ,aAAa;YACpC,QAAQ,QAAQ,MAAM;YACtB,eAAe,QAAQ,aAAa;YACpC,kBAAkB,QAAQ,gBAAgB;YAC1C,QAAQ,QAAQ,MAAM;YACtB,WAAW,QAAQ,SAAS;QAC9B;QACA,gBAAgB;IAClB;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC1D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAc,cAAc;;0CACxC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM;;sDACrB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DAAE,iBAAiB,iBAAiB;;;;;;0DAChD,8OAAC,kIAAA,CAAA,oBAAiB;0DACf,iBAAiB,+BAA+B;;;;;;;;;;;;kDAGrD,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAa;;;;;;0EAC5B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,SAAS,UAAU;gEAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAC7E,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;;0EAC3B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,SAAS,SAAS;gEACzB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAC5E,QAAQ;;;;;;;;;;;;;;;;;;0DAKd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEACxE,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEACxE,QAAQ;;;;;;;;;;;;;;;;;;0DAKd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAgB;;;;;;0EAC/B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,aAAa;gEAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAChF,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAS;;;;;;0EACxB,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,SAAS,MAAM;gEAAE,eAAe,CAAC,QAAuC,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,QAAQ;wEAAM,CAAC;;kFACpI,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;0FACzB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;0FAC3B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMlC,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC1E,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAgB;;;;;;kEAC/B,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,aAAa;wDAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAChF,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAmB;;;;;;0EAClC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,KAAI;gEACJ,OAAO,SAAS,gBAAgB;gEAChC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wEAAE,CAAC;gEAClG,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAS;;;;;;0EACxB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,KAAI;gEACJ,OAAO,SAAS,MAAM;gEACtB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,QAAQ,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wEAAE,CAAC;gEACxF,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;;0EAC3B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,SAAS;gEACzB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAC5E,QAAQ;;;;;;;;;;;;;;;;;;0DAKd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,SAAQ;wDAAU,SAAS;kEAAW;;;;;;kEAG5D,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;kEACV,iBAAiB,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;;oCAAC;oCACP,iBAAiB,MAAM;oCAAC;;;;;;;;;;;;;kCAGpC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGf,8OAAC,iIAAA,CAAA,YAAS;8CACP,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,iIAAA,CAAA,WAAQ;;8DACP,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAe,GAAG,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,SAAS,EAAE;;;;;;8DAChF,8OAAC,iIAAA,CAAA,YAAS;8DAAE,QAAQ,KAAK;;;;;;8DACzB,8OAAC,iIAAA,CAAA,YAAS;8DAAE,QAAQ,aAAa;;;;;;8DACjC,8OAAC,iIAAA,CAAA,YAAS;;wDAAE,QAAQ,gBAAgB;wDAAC;;;;;;;8DACrC,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,QAAQ,MAAM,KAAK,WAAW,YAAY;kEACvD,QAAQ,MAAM;;;;;;;;;;;8DAGnB,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAC3B,cAAA,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAK,SAAS,IAAM,WAAW;0EAC1D,cAAA,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAK,SAAS,IAAM,aAAa,QAAQ,EAAE;0EACtE,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CAnBX,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BzC", "debugId": null}}]}