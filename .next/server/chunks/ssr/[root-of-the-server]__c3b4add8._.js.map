{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/layout/teacher-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  Users,\n  Calendar,\n  Clock,\n  MessageSquare,\n  Settings,\n  LogOut,\n  GraduationCap,\n} from 'lucide-react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { toast } from 'sonner';\nimport { useRouter } from 'next/navigation';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/teacher', icon: LayoutDashboard },\n  { name: 'My Students', href: '/teacher/students', icon: Users },\n  { name: 'Attendance', href: '/teacher/attendance', icon: Calendar },\n  { name: 'My Timetable', href: '/teacher/timetable', icon: Clock },\n  { name: 'Performance Remarks', href: '/teacher/remarks', icon: MessageSquare },\n  { name: 'Settings', href: '/teacher/settings', icon: Settings },\n];\n\nexport function TeacherSidebar() {\n  const pathname = usePathname();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      // Demo mode - clear localStorage\n      if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('userId');\n        localStorage.removeItem('teacherName');\n        toast.success('Logged out successfully');\n        router.push('/login');\n        return;\n      }\n\n      // Real logout would go here\n      toast.success('Logged out successfully');\n      router.push('/login');\n    } catch (error) {\n      toast.error('Error logging out');\n    }\n  };\n\n  // Get teacher name from localStorage for demo\n  const teacherName = typeof window !== 'undefined' ? localStorage.getItem('teacherName') || 'Teacher' : 'Teacher';\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-white border-r border-gray-200\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b border-gray-200\">\n        <GraduationCap className=\"h-8 w-8 text-green-600\" />\n        <span className=\"ml-2 text-xl font-semibold text-gray-900\">\n          Teacher Portal\n        </span>\n      </div>\n\n      {/* Teacher Info */}\n      <div className=\"px-6 py-4 border-b border-gray-200\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n            <GraduationCap className=\"h-5 w-5 text-green-600\" />\n          </div>\n          <div>\n            <p className=\"text-sm font-medium text-gray-900\">{teacherName}</p>\n            <p className=\"text-xs text-gray-500\">Mathematics Teacher</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 space-y-1 px-3 py-4\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href;\n          return (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={cn(\n                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',\n                isActive\n                  ? 'bg-green-50 text-green-700 border-r-2 border-green-700'\n                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n              )}\n            >\n              <item.icon\n                className={cn(\n                  'mr-3 h-5 w-5 flex-shrink-0',\n                  isActive ? 'text-green-700' : 'text-gray-400 group-hover:text-gray-500'\n                )}\n              />\n              {item.name}\n            </Link>\n          );\n        })}\n      </nav>\n\n      {/* Logout Button */}\n      <div className=\"p-3 border-t border-gray-200\">\n        <Button\n          variant=\"ghost\"\n          className=\"w-full justify-start text-gray-700 hover:bg-gray-50\"\n          onClick={handleLogout}\n        >\n          <LogOut className=\"mr-3 h-5 w-5\" />\n          Logout\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAhBA;;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM,4NAAA,CAAA,kBAAe;IAAC;IAC7D;QAAE,MAAM;QAAe,MAAM;QAAqB,MAAM,oMAAA,CAAA,QAAK;IAAC;IAC9D;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAClE;QAAE,MAAM;QAAgB,MAAM;QAAsB,MAAM,oMAAA,CAAA,QAAK;IAAC;IAChE;QAAE,MAAM;QAAuB,MAAM;QAAoB,MAAM,wNAAA,CAAA,gBAAa;IAAC;IAC7E;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CAC/D;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,iCAAiC;YACjC,wCAAkD;gBAChD,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;gBACZ;YACF;;QAKF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,8CAA8C;IAC9C,MAAM,cAAc,6EAAmF;IAEvG,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC;wBAAK,WAAU;kCAA2C;;;;;;;;;;;;0BAM7D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WACI,2DACA;;0CAGN,8OAAC,KAAK,IAAI;gCACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,WAAW,mBAAmB;;;;;;4BAGjC,KAAK,IAAI;;uBAfL,KAAK,IAAI;;;;;gBAkBpB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,WAAU;oBACV,SAAS;;sCAET,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/app/teacher/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { TeacherSidebar } from '@/components/layout/teacher-sidebar';\n\nexport default function TeacherLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const [loading, setLoading] = useState(true);\n  const [authenticated, setAuthenticated] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        // Demo mode - check localStorage for teacher role\n        if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {\n          const userRole = localStorage.getItem('userRole');\n          \n          if (userRole !== 'teacher') {\n            router.push('/login');\n            return;\n          }\n          \n          setAuthenticated(true);\n          setLoading(false);\n          return;\n        }\n\n        // Real authentication check would go here\n        setAuthenticated(true);\n      } catch (error) {\n        router.push('/login');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, [router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-green-600\"></div>\n      </div>\n    );\n  }\n\n  if (!authenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"h-screen flex bg-gray-50\">\n      <TeacherSidebar />\n      <main className=\"flex-1 overflow-auto\">\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,kDAAkD;gBAClD,wCAAkD;oBAChD,MAAM,WAAW,aAAa,OAAO,CAAC;oBAEtC,IAAI,aAAa,WAAW;wBAC1B,OAAO,IAAI,CAAC;wBACZ;oBACF;oBAEA,iBAAiB;oBACjB,WAAW;oBACX;gBACF;;YAIF,EAAE,OAAO,OAAO;gBACd,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kJAAA,CAAA,iBAAc;;;;;0BACf,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}