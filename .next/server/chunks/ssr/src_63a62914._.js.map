{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/app/teacher/timetable/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { Badge } from '@/components/ui/badge';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Clock, Calendar, MapPin, Users, BookOpen } from 'lucide-react';\n\n// Mock data for teacher's timetable\nconst mockTimetable = [\n  {\n    id: '1',\n    day: 'Monday',\n    day_number: 1,\n    time: '09:00 - 10:00',\n    start_time: '09:00',\n    end_time: '10:00',\n    subject: 'Mathematics',\n    class: 'Grade 5-A',\n    room: 'Room 101',\n    students: 25,\n  },\n  {\n    id: '2',\n    day: 'Monday',\n    day_number: 1,\n    time: '10:30 - 11:30',\n    start_time: '10:30',\n    end_time: '11:30',\n    subject: 'Mathematics',\n    class: 'Grade 5-B',\n    room: 'Room 102',\n    students: 23,\n  },\n  {\n    id: '3',\n    day: 'Monday',\n    day_number: 1,\n    time: '14:00 - 15:00',\n    start_time: '14:00',\n    end_time: '15:00',\n    subject: 'Mathematics',\n    class: 'Grade 6-A',\n    room: 'Room 103',\n    students: 27,\n  },\n  {\n    id: '4',\n    day: 'Tuesday',\n    day_number: 2,\n    time: '09:00 - 10:00',\n    start_time: '09:00',\n    end_time: '10:00',\n    subject: 'Mathematics',\n    class: 'Grade 5-A',\n    room: 'Room 101',\n    students: 25,\n  },\n  {\n    id: '5',\n    day: 'Tuesday',\n    day_number: 2,\n    time: '11:00 - 12:00',\n    start_time: '11:00',\n    end_time: '12:00',\n    subject: 'Mathematics',\n    class: 'Grade 6-A',\n    room: 'Room 103',\n    students: 27,\n  },\n  {\n    id: '6',\n    day: 'Wednesday',\n    day_number: 3,\n    time: '10:00 - 11:00',\n    start_time: '10:00',\n    end_time: '11:00',\n    subject: 'Mathematics',\n    class: 'Grade 5-B',\n    room: 'Room 102',\n    students: 23,\n  },\n  {\n    id: '7',\n    day: 'Wednesday',\n    day_number: 3,\n    time: '13:00 - 14:00',\n    start_time: '13:00',\n    end_time: '14:00',\n    subject: 'Mathematics',\n    class: 'Grade 6-A',\n    room: 'Room 103',\n    students: 27,\n  },\n  {\n    id: '8',\n    day: 'Thursday',\n    day_number: 4,\n    time: '09:30 - 10:30',\n    start_time: '09:30',\n    end_time: '10:30',\n    subject: 'Mathematics',\n    class: 'Grade 5-A',\n    room: 'Room 101',\n    students: 25,\n  },\n  {\n    id: '9',\n    day: 'Thursday',\n    day_number: 4,\n    time: '15:00 - 16:00',\n    start_time: '15:00',\n    end_time: '16:00',\n    subject: 'Mathematics',\n    class: 'Grade 5-B',\n    room: 'Room 102',\n    students: 23,\n  },\n  {\n    id: '10',\n    day: 'Friday',\n    day_number: 5,\n    time: '08:30 - 09:30',\n    start_time: '08:30',\n    end_time: '09:30',\n    subject: 'Mathematics',\n    class: 'Grade 6-A',\n    room: 'Room 103',\n    students: 27,\n  },\n  {\n    id: '11',\n    day: 'Friday',\n    day_number: 5,\n    time: '14:30 - 15:30',\n    start_time: '14:30',\n    end_time: '15:30',\n    subject: 'Mathematics',\n    class: 'Grade 5-A',\n    room: 'Room 101',\n    students: 25,\n  },\n];\n\nconst daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];\nconst timeSlots = [\n  '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', \n  '12:00', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00'\n];\n\nexport default function TeacherTimetablePage() {\n  const [selectedDay, setSelectedDay] = useState<string | null>(null);\n\n  const generateTimetableGrid = () => {\n    const grid: { [key: string]: any } = {};\n    \n    daysOfWeek.forEach(day => {\n      timeSlots.forEach(time => {\n        const key = `${day}-${time}`;\n        const entry = mockTimetable.find(\n          item => item.day === day && item.start_time === time\n        );\n        grid[key] = entry || null;\n      });\n    });\n\n    return grid;\n  };\n\n  const timetableGrid = generateTimetableGrid();\n\n  const getTodaysClasses = () => {\n    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });\n    return mockTimetable.filter(item => item.day === today);\n  };\n\n  const getWeeklyStats = () => {\n    const totalClasses = mockTimetable.length;\n    const uniqueClasses = new Set(mockTimetable.map(item => item.class)).size;\n    const totalStudents = mockTimetable.reduce((sum, item) => sum + item.students, 0);\n    \n    return {\n      totalClasses,\n      uniqueClasses,\n      totalStudents,\n      averageClassSize: Math.round(totalStudents / totalClasses),\n    };\n  };\n\n  const todaysClasses = getTodaysClasses();\n  const weeklyStats = getWeeklyStats();\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">My Timetable</h1>\n        <p className=\"text-gray-600\">View your weekly class schedule and upcoming sessions</p>\n      </div>\n\n      {/* Weekly Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Weekly Classes</p>\n                <p className=\"text-2xl font-bold text-blue-600\">{weeklyStats.totalClasses}</p>\n              </div>\n              <div className=\"p-3 rounded-full bg-blue-50\">\n                <Calendar className=\"h-6 w-6 text-blue-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Different Classes</p>\n                <p className=\"text-2xl font-bold text-green-600\">{weeklyStats.uniqueClasses}</p>\n              </div>\n              <div className=\"p-3 rounded-full bg-green-50\">\n                <BookOpen className=\"h-6 w-6 text-green-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Students</p>\n                <p className=\"text-2xl font-bold text-purple-600\">{weeklyStats.totalStudents}</p>\n              </div>\n              <div className=\"p-3 rounded-full bg-purple-50\">\n                <Users className=\"h-6 w-6 text-purple-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Avg Class Size</p>\n                <p className=\"text-2xl font-bold text-orange-600\">{weeklyStats.averageClassSize}</p>\n              </div>\n              <div className=\"p-3 rounded-full bg-orange-50\">\n                <Users className=\"h-6 w-6 text-orange-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Today's Classes */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Clock className=\"h-5 w-5\" />\n            <span>Today's Classes</span>\n          </CardTitle>\n          <CardDescription>\n            Your schedule for today ({new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })})\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {todaysClasses.length > 0 ? (\n            <div className=\"space-y-4\">\n              {todaysClasses.map((cls) => (\n                <div key={cls.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                    <div>\n                      <p className=\"font-medium text-gray-900\">{cls.class}</p>\n                      <p className=\"text-sm text-gray-600\">{cls.subject}</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-6 text-sm text-gray-600\">\n                    <div className=\"flex items-center space-x-1\">\n                      <Clock className=\"h-4 w-4\" />\n                      <span>{cls.time}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      <MapPin className=\"h-4 w-4\" />\n                      <span>{cls.room}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      <Users className=\"h-4 w-4\" />\n                      <span>{cls.students} students</span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8 text-gray-500\">\n              <Calendar className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n              <p>No classes scheduled for today</p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Weekly Timetable Grid */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Calendar className=\"h-5 w-5\" />\n            <span>Weekly Timetable</span>\n          </CardTitle>\n          <CardDescription>\n            Your complete weekly schedule\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead className=\"w-20\">Time</TableHead>\n                  {daysOfWeek.map(day => (\n                    <TableHead key={day} className=\"text-center min-w-40\">\n                      {day}\n                    </TableHead>\n                  ))}\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {timeSlots.map(time => (\n                  <TableRow key={time}>\n                    <TableCell className=\"font-medium\">\n                      <div className=\"flex items-center space-x-1\">\n                        <Clock className=\"h-4 w-4 text-gray-400\" />\n                        <span>{time}</span>\n                      </div>\n                    </TableCell>\n                    {daysOfWeek.map(day => {\n                      const entry = timetableGrid[`${day}-${time}`];\n                      return (\n                        <TableCell key={`${day}-${time}`} className=\"text-center p-2\">\n                          {entry ? (\n                            <div className=\"bg-green-50 border border-green-200 rounded-lg p-3 text-sm\">\n                              <div className=\"font-medium text-green-900 mb-1\">\n                                {entry.class}\n                              </div>\n                              <div className=\"text-green-700 text-xs mb-1\">\n                                {entry.subject}\n                              </div>\n                              <div className=\"text-green-600 text-xs flex items-center justify-center space-x-2\">\n                                <span>{entry.room}</span>\n                                <span>•</span>\n                                <span>{entry.students}</span>\n                              </div>\n                            </div>\n                          ) : (\n                            <div className=\"text-gray-400 text-sm py-4\">-</div>\n                          )}\n                        </TableCell>\n                      );\n                    })}\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Class Details */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Class Details</CardTitle>\n          <CardDescription>Detailed information about your assigned classes</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Day</TableHead>\n                <TableHead>Time</TableHead>\n                <TableHead>Class</TableHead>\n                <TableHead>Subject</TableHead>\n                <TableHead>Room</TableHead>\n                <TableHead>Students</TableHead>\n                <TableHead>Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {mockTimetable.map((entry) => (\n                <TableRow key={entry.id}>\n                  <TableCell className=\"font-medium\">{entry.day}</TableCell>\n                  <TableCell>{entry.time}</TableCell>\n                  <TableCell>{entry.class}</TableCell>\n                  <TableCell>\n                    <Badge variant=\"outline\">{entry.subject}</Badge>\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"flex items-center space-x-1\">\n                      <MapPin className=\"h-4 w-4 text-gray-400\" />\n                      <span>{entry.room}</span>\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"flex items-center space-x-1\">\n                      <Users className=\"h-4 w-4 text-gray-400\" />\n                      <span>{entry.students}</span>\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <Button variant=\"ghost\" size=\"sm\">\n                      View Details\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AASA,oCAAoC;AACpC,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,KAAK;QACL,YAAY;QACZ,MAAM;QACN,YAAY;QACZ,UAAU;QACV,SAAS;QACT,OAAO;QACP,MAAM;QACN,UAAU;IACZ;CACD;AAED,MAAM,aAAa;IAAC;IAAU;IAAW;IAAa;IAAY;CAAS;AAC3E,MAAM,YAAY;IAChB;IAAS;IAAS;IAAS;IAAS;IAAS;IAAS;IACtD;IAAS;IAAS;IAAS;IAAS;IAAS;IAAS;IAAS;CAChE;AAEc,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,wBAAwB;QAC5B,MAAM,OAA+B,CAAC;QAEtC,WAAW,OAAO,CAAC,CAAA;YACjB,UAAU,OAAO,CAAC,CAAA;gBAChB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,MAAM;gBAC5B,MAAM,QAAQ,cAAc,IAAI,CAC9B,CAAA,OAAQ,KAAK,GAAG,KAAK,OAAO,KAAK,UAAU,KAAK;gBAElD,IAAI,CAAC,IAAI,GAAG,SAAS;YACvB;QACF;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB;IAEtB,MAAM,mBAAmB;QACvB,MAAM,QAAQ,IAAI,OAAO,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAO;QACvE,OAAO,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;IACnD;IAEA,MAAM,iBAAiB;QACrB,MAAM,eAAe,cAAc,MAAM;QACzC,MAAM,gBAAgB,IAAI,IAAI,cAAc,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG,IAAI;QACzE,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;QAE/E,OAAO;YACL;YACA;YACA;YACA,kBAAkB,KAAK,KAAK,CAAC,gBAAgB;QAC/C;IACF;IAEA,MAAM,gBAAgB;IACtB,MAAM,cAAc;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAoC,YAAY,YAAY;;;;;;;;;;;;kDAE3E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqC,YAAY,aAAa;;;;;;;;;;;;kDAE7E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAsC,YAAY,aAAa;;;;;;;;;;;;kDAE9E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMzB,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAsC,YAAY,gBAAgB;;;;;;;;;;;;kDAEjF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,kBAAe;;oCAAC;oCACW,IAAI,OAAO,kBAAkB,CAAC,SAAS;wCAAE,SAAS;wCAAQ,MAAM;wCAAW,OAAO;wCAAQ,KAAK;oCAAU;oCAAG;;;;;;;;;;;;;kCAG1I,8OAAC,gIAAA,CAAA,cAAW;kCACT,cAAc,MAAM,GAAG,kBACtB,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC;oCAAiB,WAAU;;sDAC1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA6B,IAAI,KAAK;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAAyB,IAAI,OAAO;;;;;;;;;;;;;;;;;;sDAGrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAM,IAAI,IAAI;;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAM,IAAI,IAAI;;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;gEAAM,IAAI,QAAQ;gEAAC;;;;;;;;;;;;;;;;;;;;mCAnBhB,IAAI,EAAE;;;;;;;;;iDA0BpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;0BAOX,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kDACJ,8OAAC,iIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;8DACP,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAO;;;;;;gDAC3B,WAAW,GAAG,CAAC,CAAA,oBACd,8OAAC,iIAAA,CAAA,YAAS;wDAAW,WAAU;kEAC5B;uDADa;;;;;;;;;;;;;;;;kDAMtB,8OAAC,iIAAA,CAAA,YAAS;kDACP,UAAU,GAAG,CAAC,CAAA,qBACb,8OAAC,iIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;8EAAM;;;;;;;;;;;;;;;;;oDAGV,WAAW,GAAG,CAAC,CAAA;wDACd,MAAM,QAAQ,aAAa,CAAC,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC;wDAC7C,qBACE,8OAAC,iIAAA,CAAA,YAAS;4DAAwB,WAAU;sEACzC,sBACC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,MAAM,KAAK;;;;;;kFAEd,8OAAC;wEAAI,WAAU;kFACZ,MAAM,OAAO;;;;;;kFAEhB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;0FAAM,MAAM,IAAI;;;;;;0FACjB,8OAAC;0FAAK;;;;;;0FACN,8OAAC;0FAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;qFAIzB,8OAAC;gEAAI,WAAU;0EAA6B;;;;;;2DAhBhC,GAAG,IAAI,CAAC,EAAE,MAAM;;;;;oDAoBpC;;+CA9Ba;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwC3B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0DACP,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,iIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGf,8OAAC,iIAAA,CAAA,YAAS;8CACP,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC,iIAAA,CAAA,WAAQ;;8DACP,8OAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAe,MAAM,GAAG;;;;;;8DAC7C,8OAAC,iIAAA,CAAA,YAAS;8DAAE,MAAM,IAAI;;;;;;8DACtB,8OAAC,iIAAA,CAAA,YAAS;8DAAE,MAAM,KAAK;;;;;;8DACvB,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,MAAM,OAAO;;;;;;;;;;;8DAEzC,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;0EAAM,MAAM,IAAI;;;;;;;;;;;;;;;;;8DAGrB,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;8DAGzB,8OAAC,iIAAA,CAAA,YAAS;8DACR,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAAK;;;;;;;;;;;;2CApBvB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCvC", "debugId": null}}]}