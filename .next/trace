[{"name":"hot-reloader","duration":88,"timestamp":7312488432,"id":3,"tags":{"version":"15.3.2"},"startTime":1748410891267,"traceId":"6773495287ac6375"},{"name":"setup-dev-bundler","duration":426164,"timestamp":7312367295,"id":2,"parentId":1,"tags":{},"startTime":1748410891146,"traceId":"6773495287ac6375"},{"name":"run-instrumentation-hook","duration":10,"timestamp":7312818849,"id":4,"parentId":1,"tags":{},"startTime":1748410891597,"traceId":"6773495287ac6375"},{"name":"start-dev-server","duration":705602,"timestamp":7312120121,"id":1,"tags":{"cpus":"8","platform":"darwin","memory.freeMem":"86835200","memory.totalMem":"17179869184","memory.heapSizeLimit":"8640266240","memory.rss":"278020096","memory.heapTotal":"97910784","memory.heapUsed":"75863536"},"startTime":1748410890898,"traceId":"6773495287ac6375"},{"name":"compile-path","duration":1376858,"timestamp":7335411103,"id":7,"tags":{"trigger":"/"},"startTime":1748410914190,"traceId":"6773495287ac6375"},{"name":"ensure-page","duration":1377666,"timestamp":7335410587,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1748410914189,"traceId":"6773495287ac6375"}]
[{"name":"ensure-page","duration":4480,"timestamp":7336790781,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1748410915569,"traceId":"6773495287ac6375"},{"name":"handle-request","duration":1530479,"timestamp":7335407294,"id":5,"tags":{"url":"/"},"startTime":1748410914186,"traceId":"6773495287ac6375"},{"name":"memory-usage","duration":61,"timestamp":7336937815,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"567427072","memory.heapUsed":"85704768","memory.heapTotal":"102891520"},"startTime":1748410915716,"traceId":"6773495287ac6375"},{"name":"compile-path","duration":429596,"timestamp":7385701911,"id":12,"tags":{"trigger":"/login"},"startTime":1748410964481,"traceId":"6773495287ac6375"}]
