{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/app/teacher/remarks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { Badge } from '@/components/ui/badge';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\nimport { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';\nimport { Plus, Search, MessageSquare, Star, TrendingUp, TrendingDown, Minus } from 'lucide-react';\nimport { toast } from 'sonner';\n\n// Mock data for students and remarks\nconst mockStudents = [\n  { id: '1', student_id: 'STU001', first_name: 'John', last_name: 'Doe', class: 'Grade 5-A' },\n  { id: '2', student_id: 'STU002', first_name: 'Jane', last_name: 'Smith', class: 'Grade 5-A' },\n  { id: '3', student_id: 'STU003', first_name: 'Mike', last_name: 'Johnson', class: 'Grade 5-B' },\n  { id: '4', student_id: 'STU004', first_name: 'Sarah', last_name: 'Wilson', class: 'Grade 6-A' },\n  { id: '5', student_id: 'STU005', first_name: 'David', last_name: 'Brown', class: 'Grade 6-A' },\n];\n\nconst mockRemarks = [\n  {\n    id: '1',\n    student_id: '1',\n    student_name: 'John Doe',\n    class: 'Grade 5-A',\n    subject: 'Mathematics',\n    performance_type: 'excellent',\n    remark: 'John has shown exceptional understanding of algebraic concepts. His problem-solving skills are outstanding.',\n    date: '2024-01-19',\n    teacher_name: 'Sarah Johnson',\n    status: 'sent',\n  },\n  {\n    id: '2',\n    student_id: '2',\n    student_name: 'Jane Smith',\n    class: 'Grade 5-A',\n    subject: 'Mathematics',\n    performance_type: 'good',\n    remark: 'Jane is making steady progress in mathematics. She actively participates in class discussions.',\n    date: '2024-01-18',\n    teacher_name: 'Sarah Johnson',\n    status: 'sent',\n  },\n  {\n    id: '3',\n    student_id: '3',\n    student_name: 'Mike Johnson',\n    class: 'Grade 5-B',\n    subject: 'Mathematics',\n    performance_type: 'needs_improvement',\n    remark: 'Mike needs to focus more on homework completion. Additional practice in basic arithmetic is recommended.',\n    date: '2024-01-17',\n    teacher_name: 'Sarah Johnson',\n    status: 'draft',\n  },\n];\n\nconst performanceTypes = [\n  { value: 'excellent', label: 'Excellent', icon: Star, color: 'text-green-600', bgColor: 'bg-green-50' },\n  { value: 'good', label: 'Good', icon: TrendingUp, color: 'text-blue-600', bgColor: 'bg-blue-50' },\n  { value: 'average', label: 'Average', icon: Minus, color: 'text-yellow-600', bgColor: 'bg-yellow-50' },\n  { value: 'needs_improvement', label: 'Needs Improvement', icon: TrendingDown, color: 'text-red-600', bgColor: 'bg-red-50' },\n];\n\nexport default function TeacherRemarksPage() {\n  const [remarks, setRemarks] = useState(mockRemarks);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [formData, setFormData] = useState({\n    student_id: '',\n    subject: 'Mathematics',\n    performance_type: 'good',\n    remark: '',\n  });\n\n  const filteredRemarks = remarks.filter(remark =>\n    remark.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    remark.class.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    const selectedStudent = mockStudents.find(s => s.id === formData.student_id);\n    if (!selectedStudent) return;\n\n    const newRemark = {\n      id: Date.now().toString(),\n      student_id: formData.student_id,\n      student_name: `${selectedStudent.first_name} ${selectedStudent.last_name}`,\n      class: selectedStudent.class,\n      subject: formData.subject,\n      performance_type: formData.performance_type,\n      remark: formData.remark,\n      date: new Date().toISOString().split('T')[0],\n      teacher_name: 'Sarah Johnson',\n      status: 'sent',\n    };\n\n    setRemarks(prev => [newRemark, ...prev]);\n    toast.success('Performance remark added successfully');\n    \n    setFormData({\n      student_id: '',\n      subject: 'Mathematics',\n      performance_type: 'good',\n      remark: '',\n    });\n    setIsDialogOpen(false);\n  };\n\n  const getPerformanceBadge = (type: string) => {\n    const performanceType = performanceTypes.find(p => p.value === type);\n    if (!performanceType) return null;\n\n    return (\n      <Badge variant=\"outline\" className={`${performanceType.bgColor} ${performanceType.color} border-current`}>\n        <performanceType.icon className=\"h-3 w-3 mr-1\" />\n        {performanceType.label}\n      </Badge>\n    );\n  };\n\n  const getStatusBadge = (status: string) => {\n    const variants = {\n      sent: 'default',\n      draft: 'secondary',\n    } as const;\n\n    return (\n      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>\n        {status}\n      </Badge>\n    );\n  };\n\n  const getInitials = (name: string) => {\n    const names = name.split(' ');\n    return names.map(n => n.charAt(0)).join('');\n  };\n\n  const remarkStats = {\n    total: remarks.length,\n    sent: remarks.filter(r => r.status === 'sent').length,\n    drafts: remarks.filter(r => r.status === 'draft').length,\n    thisWeek: remarks.filter(r => {\n      const remarkDate = new Date(r.date);\n      const weekAgo = new Date();\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      return remarkDate >= weekAgo;\n    }).length,\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Performance Remarks</h1>\n          <p className=\"text-gray-600\">Send performance feedback to students and parents</p>\n        </div>\n        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\n          <DialogTrigger asChild>\n            <Button>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Remark\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-2xl\">\n            <DialogHeader>\n              <DialogTitle>Add Performance Remark</DialogTitle>\n              <DialogDescription>\n                Send performance feedback for a student\n              </DialogDescription>\n            </DialogHeader>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"student\">Student</Label>\n                  <Select value={formData.student_id} onValueChange={(value) => setFormData(prev => ({ ...prev, student_id: value }))}>\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Select student\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {mockStudents.map(student => (\n                        <SelectItem key={student.id} value={student.id}>\n                          {student.first_name} {student.last_name} ({student.class})\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n                <div>\n                  <Label htmlFor=\"subject\">Subject</Label>\n                  <Select value={formData.subject} onValueChange={(value) => setFormData(prev => ({ ...prev, subject: value }))}>\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"Mathematics\">Mathematics</SelectItem>\n                      <SelectItem value=\"English\">English</SelectItem>\n                      <SelectItem value=\"Science\">Science</SelectItem>\n                      <SelectItem value=\"History\">History</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n\n              <div>\n                <Label htmlFor=\"performance_type\">Performance Type</Label>\n                <Select value={formData.performance_type} onValueChange={(value) => setFormData(prev => ({ ...prev, performance_type: value }))}>\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {performanceTypes.map(type => (\n                      <SelectItem key={type.value} value={type.value}>\n                        <div className=\"flex items-center space-x-2\">\n                          <type.icon className={`h-4 w-4 ${type.color}`} />\n                          <span>{type.label}</span>\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div>\n                <Label htmlFor=\"remark\">Remark</Label>\n                <Textarea\n                  id=\"remark\"\n                  value={formData.remark}\n                  onChange={(e) => setFormData(prev => ({ ...prev, remark: e.target.value }))}\n                  placeholder=\"Enter detailed performance feedback...\"\n                  rows={4}\n                  required\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-2\">\n                <Button type=\"button\" variant=\"outline\" onClick={() => setIsDialogOpen(false)}>\n                  Cancel\n                </Button>\n                <Button type=\"submit\">\n                  <MessageSquare className=\"h-4 w-4 mr-2\" />\n                  Send Remark\n                </Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Remarks</p>\n                <p className=\"text-2xl font-bold text-blue-600\">{remarkStats.total}</p>\n              </div>\n              <div className=\"p-3 rounded-full bg-blue-50\">\n                <MessageSquare className=\"h-6 w-6 text-blue-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Sent</p>\n                <p className=\"text-2xl font-bold text-green-600\">{remarkStats.sent}</p>\n              </div>\n              <div className=\"p-3 rounded-full bg-green-50\">\n                <MessageSquare className=\"h-6 w-6 text-green-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Drafts</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">{remarkStats.drafts}</p>\n              </div>\n              <div className=\"p-3 rounded-full bg-yellow-50\">\n                <MessageSquare className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">This Week</p>\n                <p className=\"text-2xl font-bold text-purple-600\">{remarkStats.thisWeek}</p>\n              </div>\n              <div className=\"p-3 rounded-full bg-purple-50\">\n                <MessageSquare className=\"h-6 w-6 text-purple-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Search */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n            <Input\n              placeholder=\"Search remarks by student name or class...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Remarks List */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Performance Remarks</CardTitle>\n          <CardDescription>\n            {filteredRemarks.length} remarks found\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Student</TableHead>\n                <TableHead>Subject</TableHead>\n                <TableHead>Performance</TableHead>\n                <TableHead>Date</TableHead>\n                <TableHead>Status</TableHead>\n                <TableHead>Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredRemarks.map((remark) => (\n                <TableRow key={remark.id}>\n                  <TableCell>\n                    <div className=\"flex items-center space-x-3\">\n                      <Avatar>\n                        <AvatarFallback className=\"bg-blue-100 text-blue-600\">\n                          {getInitials(remark.student_name)}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <p className=\"font-medium text-gray-900\">{remark.student_name}</p>\n                        <p className=\"text-sm text-gray-500\">{remark.class}</p>\n                      </div>\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <Badge variant=\"outline\">{remark.subject}</Badge>\n                  </TableCell>\n                  <TableCell>{getPerformanceBadge(remark.performance_type)}</TableCell>\n                  <TableCell>{remark.date}</TableCell>\n                  <TableCell>{getStatusBadge(remark.status)}</TableCell>\n                  <TableCell>\n                    <div className=\"flex space-x-2\">\n                      <Button variant=\"ghost\" size=\"sm\">\n                        View\n                      </Button>\n                      <Button variant=\"ghost\" size=\"sm\">\n                        Edit\n                      </Button>\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAfA;;;;;;;;;;;;;;AAiBA,qCAAqC;AACrC,MAAM,eAAe;IACnB;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAQ,WAAW;QAAO,OAAO;IAAY;IAC1F;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAQ,WAAW;QAAS,OAAO;IAAY;IAC5F;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAQ,WAAW;QAAW,OAAO;IAAY;IAC9F;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAS,WAAW;QAAU,OAAO;IAAY;IAC9F;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAS,WAAW;QAAS,OAAO;IAAY;CAC9F;AAED,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,YAAY;QACZ,cAAc;QACd,OAAO;QACP,SAAS;QACT,kBAAkB;QAClB,QAAQ;QACR,MAAM;QACN,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,cAAc;QACd,OAAO;QACP,SAAS;QACT,kBAAkB;QAClB,QAAQ;QACR,MAAM;QACN,cAAc;QACd,QAAQ;IACV;IACA;QACE,IAAI;QACJ,YAAY;QACZ,cAAc;QACd,OAAO;QACP,SAAS;QACT,kBAAkB;QAClB,QAAQ;QACR,MAAM;QACN,cAAc;QACd,QAAQ;IACV;CACD;AAED,MAAM,mBAAmB;IACvB;QAAE,OAAO;QAAa,OAAO;QAAa,MAAM,qMAAA,CAAA,OAAI;QAAE,OAAO;QAAkB,SAAS;IAAc;IACtG;QAAE,OAAO;QAAQ,OAAO;QAAQ,MAAM,qNAAA,CAAA,aAAU;QAAE,OAAO;QAAiB,SAAS;IAAa;IAChG;QAAE,OAAO;QAAW,OAAO;QAAW,MAAM,uMAAA,CAAA,QAAK;QAAE,OAAO;QAAmB,SAAS;IAAe;IACrG;QAAE,OAAO;QAAqB,OAAO;QAAqB,MAAM,yNAAA,CAAA,eAAY;QAAE,OAAO;QAAgB,SAAS;IAAY;CAC3H;AAEc,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,YAAY;QACZ,SAAS;QACT,kBAAkB;QAClB,QAAQ;IACV;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,OAAO,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG5D,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,MAAM,kBAAkB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,UAAU;QAC3E,IAAI,CAAC,iBAAiB;QAEtB,MAAM,YAAY;YAChB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY,SAAS,UAAU;YAC/B,cAAc,GAAG,gBAAgB,UAAU,CAAC,CAAC,EAAE,gBAAgB,SAAS,EAAE;YAC1E,OAAO,gBAAgB,KAAK;YAC5B,SAAS,SAAS,OAAO;YACzB,kBAAkB,SAAS,gBAAgB;YAC3C,QAAQ,SAAS,MAAM;YACvB,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5C,cAAc;YACd,QAAQ;QACV;QAEA,WAAW,CAAA,OAAQ;gBAAC;mBAAc;aAAK;QACvC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAEd,YAAY;YACV,YAAY;YACZ,SAAS;YACT,kBAAkB;YAClB,QAAQ;QACV;QACA,gBAAgB;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAC/D,IAAI,CAAC,iBAAiB,OAAO;QAE7B,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAW,GAAG,gBAAgB,OAAO,CAAC,CAAC,EAAE,gBAAgB,KAAK,CAAC,eAAe,CAAC;;8BACtG,6LAAC,gBAAgB,IAAI;oBAAC,WAAU;;;;;;gBAC/B,gBAAgB,KAAK;;;;;;;IAG5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW;YACf,MAAM;YACN,OAAO;QACT;QAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,SAAS,QAAQ,CAAC,OAAgC,IAAI;sBAC1D;;;;;;IAGP;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,OAAO,MAAM,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC;IAC1C;IAEA,MAAM,cAAc;QAClB,OAAO,QAAQ,MAAM;QACrB,MAAM,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QACrD,QAAQ,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;QACxD,UAAU,QAAQ,MAAM,CAAC,CAAA;YACvB,MAAM,aAAa,IAAI,KAAK,EAAE,IAAI;YAClC,MAAM,UAAU,IAAI;YACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;YACpC,OAAO,cAAc;QACvB,GAAG,MAAM;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAc,cAAc;;0CACxC,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;;sDACL,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,6LAAC,qIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAIrB,6LAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,6LAAC,qIAAA,CAAA,SAAM;gEAAC,OAAO,SAAS,UAAU;gEAAE,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,YAAY;wEAAM,CAAC;;kFAC/G,6LAAC,qIAAA,CAAA,gBAAa;kFACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4EAAC,aAAY;;;;;;;;;;;kFAE3B,6LAAC,qIAAA,CAAA,gBAAa;kFACX,aAAa,GAAG,CAAC,CAAA,wBAChB,6LAAC,qIAAA,CAAA,aAAU;gFAAkB,OAAO,QAAQ,EAAE;;oFAC3C,QAAQ,UAAU;oFAAC;oFAAE,QAAQ,SAAS;oFAAC;oFAAG,QAAQ,KAAK;oFAAC;;+EAD1C,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;kEAOnC,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,6LAAC,qIAAA,CAAA,SAAM;gEAAC,OAAO,SAAS,OAAO;gEAAE,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,SAAS;wEAAM,CAAC;;kFACzG,6LAAC,qIAAA,CAAA,gBAAa;kFACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,6LAAC,qIAAA,CAAA,gBAAa;;0FACZ,6LAAC,qIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAc;;;;;;0FAChC,6LAAC,qIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAU;;;;;;0FAC5B,6LAAC,qIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAU;;;;;;0FAC5B,6LAAC,qIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMpC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAmB;;;;;;kEAClC,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO,SAAS,gBAAgB;wDAAE,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,kBAAkB;gEAAM,CAAC;;0EAC3H,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,6LAAC,qIAAA,CAAA,gBAAa;0EACX,iBAAiB,GAAG,CAAC,CAAA,qBACpB,6LAAC,qIAAA,CAAA,aAAU;wEAAkB,OAAO,KAAK,KAAK;kFAC5C,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,KAAK,IAAI;oFAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;8FAC7C,6LAAC;8FAAM,KAAK,KAAK;;;;;;;;;;;;uEAHJ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;0DAWnC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAS;;;;;;kEACxB,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO,SAAS,MAAM;wDACtB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACzE,aAAY;wDACZ,MAAM;wDACN,QAAQ;;;;;;;;;;;;0DAIZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,SAAQ;wDAAU,SAAS,IAAM,gBAAgB;kEAAQ;;;;;;kEAG/E,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;;0EACX,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAoC,YAAY,KAAK;;;;;;;;;;;;kDAEpE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMjC,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAqC,YAAY,IAAI;;;;;;;;;;;;kDAEpE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMjC,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAsC,YAAY,MAAM;;;;;;;;;;;;kDAEvE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMjC,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAsC,YAAY,QAAQ;;;;;;;;;;;;kDAEzE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnC,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC,oIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAOlB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;;oCACb,gBAAgB,MAAM;oCAAC;;;;;;;;;;;;;kCAG5B,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8CACJ,6LAAC,oIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;0DACP,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,oIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGf,6LAAC,oIAAA,CAAA,YAAS;8CACP,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC,oIAAA,CAAA,WAAQ;;8DACP,6LAAC,oIAAA,CAAA,YAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;0EACL,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,YAAY,OAAO,YAAY;;;;;;;;;;;0EAGpC,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA6B,OAAO,YAAY;;;;;;kFAC7D,6LAAC;wEAAE,WAAU;kFAAyB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;8DAIxD,6LAAC,oIAAA,CAAA,YAAS;8DACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,OAAO,OAAO;;;;;;;;;;;8DAE1C,6LAAC,oIAAA,CAAA,YAAS;8DAAE,oBAAoB,OAAO,gBAAgB;;;;;;8DACvD,6LAAC,oIAAA,CAAA,YAAS;8DAAE,OAAO,IAAI;;;;;;8DACvB,6LAAC,oIAAA,CAAA,YAAS;8DAAE,eAAe,OAAO,MAAM;;;;;;8DACxC,6LAAC,oIAAA,CAAA,YAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAAK;;;;;;0EAGlC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAAK;;;;;;;;;;;;;;;;;;2CAzBzB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCxC;GAnUwB;KAAA", "debugId": null}}]}