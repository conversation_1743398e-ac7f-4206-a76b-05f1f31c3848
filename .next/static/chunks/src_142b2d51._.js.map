{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/school-admin-dashboard/src/app/teacher/attendance/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { Badge } from '@/components/ui/badge';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Label } from '@/components/ui/label';\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Calendar, Save, History, Edit, Users } from 'lucide-react';\nimport { toast } from 'sonner';\n\n// Mock data for attendance\nconst mockStudents = [\n  { id: '1', student_id: 'STU001', first_name: '<PERSON>', last_name: '<PERSON><PERSON>', class: 'Grade 5-A' },\n  { id: '2', student_id: 'STU002', first_name: '<PERSON>', last_name: '<PERSON>', class: 'Grade 5-A' },\n  { id: '3', student_id: 'STU003', first_name: 'Mike', last_name: 'Johnson', class: 'Grade 5-A' },\n  { id: '4', student_id: 'STU004', first_name: 'Sarah', last_name: 'Wilson', class: 'Grade 5-A' },\n  { id: '5', student_id: 'STU005', first_name: 'David', last_name: 'Brown', class: 'Grade 5-A' },\n];\n\nconst mockClasses = [\n  { id: 'grade5a', name: 'Grade 5-A', students: 25 },\n  { id: 'grade5b', name: 'Grade 5-B', students: 23 },\n  { id: 'grade6a', name: 'Grade 6-A', students: 27 },\n];\n\nconst mockAttendanceHistory = [\n  { date: '2024-01-19', class: 'Grade 5-A', present: 23, absent: 2, late: 0, total: 25 },\n  { date: '2024-01-18', class: 'Grade 5-A', present: 24, absent: 1, late: 0, total: 25 },\n  { date: '2024-01-17', class: 'Grade 5-A', present: 22, absent: 2, late: 1, total: 25 },\n  { date: '2024-01-16', class: 'Grade 5-A', present: 25, absent: 0, late: 0, total: 25 },\n  { date: '2024-01-15', class: 'Grade 5-A', present: 23, absent: 1, late: 1, total: 25 },\n];\n\nexport default function TeacherAttendancePage() {\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n  const [selectedClass, setSelectedClass] = useState('grade5a');\n  const [attendance, setAttendance] = useState<Record<string, 'present' | 'absent' | 'late' | 'excused'>>({});\n  const [editMode, setEditMode] = useState(false);\n\n  const handleAttendanceChange = (studentId: string, status: 'present' | 'absent' | 'late' | 'excused') => {\n    setAttendance(prev => ({\n      ...prev,\n      [studentId]: status\n    }));\n  };\n\n  const handleSaveAttendance = () => {\n    // In a real app, this would save to the database\n    toast.success('Attendance saved successfully');\n    setEditMode(false);\n  };\n\n  const handleEditAttendance = () => {\n    setEditMode(true);\n    toast.info('Edit mode enabled. Make your changes and save.');\n  };\n\n  const getStatusBadge = (status: string) => {\n    const variants = {\n      present: 'default',\n      absent: 'destructive',\n      late: 'secondary',\n      excused: 'outline'\n    } as const;\n\n    return (\n      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>\n        {status}\n      </Badge>\n    );\n  };\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`;\n  };\n\n  const selectedClassData = mockClasses.find(c => c.id === selectedClass);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-gray-900\">Attendance Management</h1>\n        <p className=\"text-gray-600\">Mark and manage student attendance for your classes</p>\n      </div>\n\n      <Tabs defaultValue=\"mark\" className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-2\">\n          <TabsTrigger value=\"mark\" className=\"flex items-center space-x-2\">\n            <Calendar className=\"h-4 w-4\" />\n            <span>Mark Attendance</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"history\" className=\"flex items-center space-x-2\">\n            <History className=\"h-4 w-4\" />\n            <span>Attendance History</span>\n          </TabsTrigger>\n        </TabsList>\n\n        {/* Mark Attendance Tab */}\n        <TabsContent value=\"mark\">\n          {/* Filters */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Mark Attendance</CardTitle>\n              <CardDescription>Select date and class to mark attendance</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <Label htmlFor=\"date\">Date</Label>\n                  <Input\n                    id=\"date\"\n                    type=\"date\"\n                    value={selectedDate}\n                    onChange={(e) => setSelectedDate(e.target.value)}\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"class\">Class</Label>\n                  <Select value={selectedClass} onValueChange={setSelectedClass}>\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {mockClasses.map(cls => (\n                        <SelectItem key={cls.id} value={cls.id}>\n                          {cls.name} ({cls.students} students)\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n                <div className=\"flex items-end space-x-2\">\n                  <Button onClick={handleSaveAttendance} className=\"flex-1\">\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    Save Attendance\n                  </Button>\n                  {!editMode && (\n                    <Button variant=\"outline\" onClick={handleEditAttendance}>\n                      <Edit className=\"h-4 w-4 mr-2\" />\n                      Edit\n                    </Button>\n                  )}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Attendance Summary */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Present</p>\n                    <p className=\"text-2xl font-bold text-green-600\">\n                      {Object.values(attendance).filter(status => status === 'present').length}\n                    </p>\n                  </div>\n                  <div className=\"p-3 rounded-full bg-green-50\">\n                    <Users className=\"h-6 w-6 text-green-600\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Absent</p>\n                    <p className=\"text-2xl font-bold text-red-600\">\n                      {Object.values(attendance).filter(status => status === 'absent').length}\n                    </p>\n                  </div>\n                  <div className=\"p-3 rounded-full bg-red-50\">\n                    <Users className=\"h-6 w-6 text-red-600\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Late</p>\n                    <p className=\"text-2xl font-bold text-yellow-600\">\n                      {Object.values(attendance).filter(status => status === 'late').length}\n                    </p>\n                  </div>\n                  <div className=\"p-3 rounded-full bg-yellow-50\">\n                    <Users className=\"h-6 w-6 text-yellow-600\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-600\">Total</p>\n                    <p className=\"text-2xl font-bold text-blue-600\">\n                      {selectedClassData?.students || 0}\n                    </p>\n                  </div>\n                  <div className=\"p-3 rounded-full bg-blue-50\">\n                    <Users className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Students Attendance */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Students - {selectedClassData?.name}</CardTitle>\n              <CardDescription>\n                Mark attendance for {selectedDate}\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Student</TableHead>\n                    <TableHead>Student ID</TableHead>\n                    <TableHead>Status</TableHead>\n                    <TableHead>Actions</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {mockStudents.map((student) => (\n                    <TableRow key={student.id}>\n                      <TableCell>\n                        <div className=\"flex items-center space-x-3\">\n                          <Avatar>\n                            <AvatarFallback className=\"bg-blue-100 text-blue-600\">\n                              {getInitials(student.first_name, student.last_name)}\n                            </AvatarFallback>\n                          </Avatar>\n                          <div>\n                            <p className=\"font-medium text-gray-900\">\n                              {student.first_name} {student.last_name}\n                            </p>\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"font-medium\">{student.student_id}</TableCell>\n                      <TableCell>\n                        {attendance[student.id] ? getStatusBadge(attendance[student.id]) : (\n                          <Badge variant=\"outline\">Not Marked</Badge>\n                        )}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex space-x-2\">\n                          <Button\n                            size=\"sm\"\n                            variant={attendance[student.id] === 'present' ? 'default' : 'outline'}\n                            onClick={() => handleAttendanceChange(student.id, 'present')}\n                            disabled={!editMode && attendance[student.id] !== undefined}\n                          >\n                            Present\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant={attendance[student.id] === 'absent' ? 'destructive' : 'outline'}\n                            onClick={() => handleAttendanceChange(student.id, 'absent')}\n                            disabled={!editMode && attendance[student.id] !== undefined}\n                          >\n                            Absent\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant={attendance[student.id] === 'late' ? 'secondary' : 'outline'}\n                            onClick={() => handleAttendanceChange(student.id, 'late')}\n                            disabled={!editMode && attendance[student.id] !== undefined}\n                          >\n                            Late\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant={attendance[student.id] === 'excused' ? 'outline' : 'outline'}\n                            onClick={() => handleAttendanceChange(student.id, 'excused')}\n                            disabled={!editMode && attendance[student.id] !== undefined}\n                          >\n                            Excused\n                          </Button>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        {/* Attendance History Tab */}\n        <TabsContent value=\"history\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Attendance History</CardTitle>\n              <CardDescription>View past attendance records for your classes</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Date</TableHead>\n                    <TableHead>Class</TableHead>\n                    <TableHead>Present</TableHead>\n                    <TableHead>Absent</TableHead>\n                    <TableHead>Late</TableHead>\n                    <TableHead>Total</TableHead>\n                    <TableHead>Attendance Rate</TableHead>\n                    <TableHead>Actions</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {mockAttendanceHistory.map((record, index) => (\n                    <TableRow key={index}>\n                      <TableCell className=\"font-medium\">{record.date}</TableCell>\n                      <TableCell>{record.class}</TableCell>\n                      <TableCell>\n                        <Badge variant=\"default\">{record.present}</Badge>\n                      </TableCell>\n                      <TableCell>\n                        <Badge variant=\"destructive\">{record.absent}</Badge>\n                      </TableCell>\n                      <TableCell>\n                        <Badge variant=\"secondary\">{record.late}</Badge>\n                      </TableCell>\n                      <TableCell>{record.total}</TableCell>\n                      <TableCell>\n                        <span className=\"font-medium\">\n                          {((record.present / record.total) * 100).toFixed(1)}%\n                        </span>\n                      </TableCell>\n                      <TableCell>\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAbA;;;;;;;;;;;;;AAeA,2BAA2B;AAC3B,MAAM,eAAe;IACnB;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAQ,WAAW;QAAO,OAAO;IAAY;IAC1F;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAQ,WAAW;QAAS,OAAO;IAAY;IAC5F;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAQ,WAAW;QAAW,OAAO;IAAY;IAC9F;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAS,WAAW;QAAU,OAAO;IAAY;IAC9F;QAAE,IAAI;QAAK,YAAY;QAAU,YAAY;QAAS,WAAW;QAAS,OAAO;IAAY;CAC9F;AAED,MAAM,cAAc;IAClB;QAAE,IAAI;QAAW,MAAM;QAAa,UAAU;IAAG;IACjD;QAAE,IAAI;QAAW,MAAM;QAAa,UAAU;IAAG;IACjD;QAAE,IAAI;QAAW,MAAM;QAAa,UAAU;IAAG;CAClD;AAED,MAAM,wBAAwB;IAC5B;QAAE,MAAM;QAAc,OAAO;QAAa,SAAS;QAAI,QAAQ;QAAG,MAAM;QAAG,OAAO;IAAG;IACrF;QAAE,MAAM;QAAc,OAAO;QAAa,SAAS;QAAI,QAAQ;QAAG,MAAM;QAAG,OAAO;IAAG;IACrF;QAAE,MAAM;QAAc,OAAO;QAAa,SAAS;QAAI,QAAQ;QAAG,MAAM;QAAG,OAAO;IAAG;IACrF;QAAE,MAAM;QAAc,OAAO;QAAa,SAAS;QAAI,QAAQ;QAAG,MAAM;QAAG,OAAO;IAAG;IACrF;QAAE,MAAM;QAAc,OAAO;QAAa,SAAS;QAAI,QAAQ;QAAG,MAAM;QAAG,OAAO;IAAG;CACtF;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACvF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6D,CAAC;IACzG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,yBAAyB,CAAC,WAAmB;QACjD,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE;YACf,CAAC;IACH;IAEA,MAAM,uBAAuB;QAC3B,iDAAiD;QACjD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QACd,YAAY;IACd;IAEA,MAAM,uBAAuB;QAC3B,YAAY;QACZ,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW;YACf,SAAS;YACT,QAAQ;YACR,MAAM;YACN,SAAS;QACX;QAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,SAAS,QAAQ,CAAC,OAAgC,IAAI;sBAC1D;;;;;;IAGP;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI;IACtD;IAEA,MAAM,oBAAoB,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEzD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAG/B,6LAAC,mIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAO,WAAU;;kCAClC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,WAAU;;kDAClC,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;;kDACrC,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;;0CAEjB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAO;;;;;;sEACtB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8DAGnD,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAQ;;;;;;sEACvB,6LAAC,qIAAA,CAAA,SAAM;4DAAC,OAAO;4DAAe,eAAe;;8EAC3C,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,6LAAC,qIAAA,CAAA,gBAAa;8EACX,YAAY,GAAG,CAAC,CAAA,oBACf,6LAAC,qIAAA,CAAA,aAAU;4EAAc,OAAO,IAAI,EAAE;;gFACnC,IAAI,IAAI;gFAAC;gFAAG,IAAI,QAAQ;gFAAC;;2EADX,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;8DAO/B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAS;4DAAsB,WAAU;;8EAC/C,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAGlC,CAAC,0BACA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,SAAS;;8EACjC,6LAAC,8MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,OAAI;kDACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EACV,OAAO,MAAM,CAAC,YAAY,MAAM,CAAC,CAAA,SAAU,WAAW,WAAW,MAAM;;;;;;;;;;;;kEAG5E,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMzB,6LAAC,mIAAA,CAAA,OAAI;kDACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EACV,OAAO,MAAM,CAAC,YAAY,MAAM,CAAC,CAAA,SAAU,WAAW,UAAU,MAAM;;;;;;;;;;;;kEAG3E,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMzB,6LAAC,mIAAA,CAAA,OAAI;kDACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EACV,OAAO,MAAM,CAAC,YAAY,MAAM,CAAC,CAAA,SAAU,WAAW,QAAQ,MAAM;;;;;;;;;;;;kEAGzE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMzB,6LAAC,mIAAA,CAAA,OAAI;kDACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,6LAAC;gEAAE,WAAU;0EACV,mBAAmB,YAAY;;;;;;;;;;;;kEAGpC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3B,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;;oDAAC;oDAAY,mBAAmB;;;;;;;0DAC1C,6LAAC,mIAAA,CAAA,kBAAe;;oDAAC;oDACM;;;;;;;;;;;;;kDAGzB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8DACJ,6LAAC,oIAAA,CAAA,cAAW;8DACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;0EACP,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,6LAAC,oIAAA,CAAA,YAAS;0EAAC;;;;;;;;;;;;;;;;;8DAGf,6LAAC,oIAAA,CAAA,YAAS;8DACP,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;8EACR,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qIAAA,CAAA,SAAM;0FACL,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oFAAC,WAAU;8FACvB,YAAY,QAAQ,UAAU,EAAE,QAAQ,SAAS;;;;;;;;;;;0FAGtD,6LAAC;0FACC,cAAA,6LAAC;oFAAE,WAAU;;wFACV,QAAQ,UAAU;wFAAC;wFAAE,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;8EAK/C,6LAAC,oIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAe,QAAQ,UAAU;;;;;;8EACtD,6LAAC,oIAAA,CAAA,YAAS;8EACP,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,eAAe,UAAU,CAAC,QAAQ,EAAE,CAAC,kBAC7D,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAU;;;;;;;;;;;8EAG7B,6LAAC,oIAAA,CAAA,YAAS;8EACR,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAS,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,YAAY,YAAY;gFAC5D,SAAS,IAAM,uBAAuB,QAAQ,EAAE,EAAE;gFAClD,UAAU,CAAC,YAAY,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK;0FACnD;;;;;;0FAGD,6LAAC,qIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAS,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,WAAW,gBAAgB;gFAC/D,SAAS,IAAM,uBAAuB,QAAQ,EAAE,EAAE;gFAClD,UAAU,CAAC,YAAY,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK;0FACnD;;;;;;0FAGD,6LAAC,qIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAS,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,SAAS,cAAc;gFAC3D,SAAS,IAAM,uBAAuB,QAAQ,EAAE,EAAE;gFAClD,UAAU,CAAC,YAAY,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK;0FACnD;;;;;;0FAGD,6LAAC,qIAAA,CAAA,SAAM;gFACL,MAAK;gFACL,SAAS,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK,YAAY,YAAY;gFAC5D,SAAS,IAAM,uBAAuB,QAAQ,EAAE,EAAE;gFAClD,UAAU,CAAC,YAAY,UAAU,CAAC,QAAQ,EAAE,CAAC,KAAK;0FACnD;;;;;;;;;;;;;;;;;;2DApDQ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAkErC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,oIAAA,CAAA,QAAK;;0DACJ,6LAAC,oIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;sEACP,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,6LAAC,oIAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;;;;;;;0DAGf,6LAAC,oIAAA,CAAA,YAAS;0DACP,sBAAsB,GAAG,CAAC,CAAC,QAAQ,sBAClC,6LAAC,oIAAA,CAAA,WAAQ;;0EACP,6LAAC,oIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAe,OAAO,IAAI;;;;;;0EAC/C,6LAAC,oIAAA,CAAA,YAAS;0EAAE,OAAO,KAAK;;;;;;0EACxB,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAW,OAAO,OAAO;;;;;;;;;;;0EAE1C,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAe,OAAO,MAAM;;;;;;;;;;;0EAE7C,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAa,OAAO,IAAI;;;;;;;;;;;0EAEzC,6LAAC,oIAAA,CAAA,YAAS;0EAAE,OAAO,KAAK;;;;;;0EACxB,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC;oEAAK,WAAU;;wEACb,CAAC,AAAC,OAAO,OAAO,GAAG,OAAO,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;0EAGxD,6LAAC,oIAAA,CAAA,YAAS;0EACR,cAAA,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAQ,MAAK;8EAC3B,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;uDApBP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCnC;GAnUwB;KAAA", "debugId": null}}]}